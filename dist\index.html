<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <title>ByeVape - Vaping Cessation Support</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="description" content="ByeVape helps you quit vaping gradually with a supportive tracking system" />

    <script
      type="module"
      src="https://unpkg.com/@ionic/pwa-elements@latest/dist/ionicpwaelements/ionicpwaelements.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/@ionic/pwa-elements@latest/dist/ionicpwaelements/ionicpwaelements.js"
    ></script>

    <link rel="icon" type="image/x-icon" href="/assets/favicon-UvKAN7pv.ico" />
    <link rel="manifest" href="/assets/manifest-BJjSYtZ0.json" />
    <meta name="theme-color" content="#3b2d56" />
    <script type="module" crossorigin src="/assets/index-BzrPJEGE.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-Br-9nj1h.css">
  </head>
  <body>
    <div class="app-layout">
      <!-- Main Content Area -->
      <main class="main-content" id="main-content">

        <!-- Onboarding Page -->
        <div class="page" id="onboarding-page" style="display: none;">
          <div class="container">
            <div class="text-center mb-4">
              <h1>Welcome to ByeVape</h1>
              <p class="text-secondary">Let's set up your personalized quit plan</p>
            </div>

            <div class="card">
              <div class="card-body">
                <div id="onboarding-step-1" class="onboarding-step">
                  <h3 class="text-center mb-4">How many times do you vape per day?</h3>
                  <div class="form-group">
                    <input type="number" id="daily-vaping-count" class="form-control"
                           placeholder="Enter number (e.g., 20)" min="1" max="100">
                  </div>
                  <button class="btn btn-primary btn-block" onclick="nextOnboardingStep()">
                    Continue
                  </button>
                </div>

                <div id="onboarding-step-2" class="onboarding-step" style="display: none;">
                  <h3 class="text-center mb-4">How fast do you want to quit?</h3>
                  <div class="d-flex flex-column gap-3">
                    <button class="btn btn-outline cessation-speed-btn" data-speed="fast" onclick="selectCessationSpeed('fast')">
                      <div>
                        <strong>Fast</strong><br>
                        <small>30 days</small>
                      </div>
                    </button>
                    <button class="btn btn-outline cessation-speed-btn" data-speed="normal" onclick="selectCessationSpeed('normal')">
                      <div>
                        <strong>Normal</strong><br>
                        <small>60 days</small>
                      </div>
                    </button>
                    <button class="btn btn-outline cessation-speed-btn" data-speed="relaxed" onclick="selectCessationSpeed('relaxed')">
                      <div>
                        <strong>Relaxed</strong><br>
                        <small>90 days</small>
                      </div>
                    </button>
                  </div>
                  <button class="btn btn-primary btn-block mt-4" id="complete-onboarding"
                          onclick="completeOnboarding()" disabled>
                    Start My Journey
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tracker Page (Main Page) -->
        <div class="page" id="tracker-page">
          <div class="container">
            <div class="text-center mb-4">
              <h1>ByeVape Tracker</h1>
              <p class="text-secondary">Day <span id="current-day">1</span> of your journey</p>
            </div>

            <!-- Countdown Timer Card -->
            <div class="card text-center">
              <div class="card-body">
                <h3 class="card-title">Next Vaping Break</h3>
                <div class="circular-progress">
                  <svg>
                    <circle class="progress-ring" cx="100" cy="100" r="90"></circle>
                    <circle class="progress-ring-fill" cx="100" cy="100" r="90"
                            stroke-dasharray="565.48" stroke-dashoffset="226.19"></circle>
                  </svg>
                  <div class="progress-text">
                    <div class="progress-time" id="countdown-timer">02:45:30</div>
                    <div class="progress-label">Until next break</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Breaks Remaining Card -->
            <div class="card">
              <div class="card-body text-center">
                <h4>Breaks Remaining Today</h4>
                <div class="d-flex justify-content-center align-items-center gap-3 mt-3">
                  <span class="text-primary" style="font-size: 2rem; font-weight: bold;" id="breaks-remaining">8</span>
                  <span class="text-secondary">of</span>
                  <span class="text-secondary" id="total-breaks">12</span>
                </div>
                <div class="progress mt-3">
                  <div class="progress-bar" id="daily-progress" style="width: 33%;"></div>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="d-flex flex-column gap-3">
              <button class="btn btn-primary btn-lg" id="take-break-btn">
                Take Vaping Break
              </button>
              <button class="btn btn-secondary" id="early-break-btn">
                Early Break (adds time to next)
              </button>
            </div>

            <!-- Daily Summary -->
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Today's Progress</h4>
              </div>
              <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                  <span>Breaks taken:</span>
                  <span id="breaks-taken">4</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span>Time saved:</span>
                  <span id="time-saved">2h 15m</span>
                </div>
                <div class="d-flex justify-content-between">
                  <span>Next reset:</span>
                  <span id="next-reset">6h 45m</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Statistics Page -->
        <div class="page" id="statistics-page" style="display: none;">
          <div class="container">
            <div class="text-center mb-4">
              <h1>Statistics</h1>
              <p class="text-secondary">Track your progress over time</p>
            </div>

            <!-- Weekly Overview -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">This Week</h3>
              </div>
              <div class="card-body">
                <div class="d-flex justify-content-between mb-3">
                  <span>Average breaks per day:</span>
                  <span class="text-primary font-weight-bold">8.2</span>
                </div>
                <div class="d-flex justify-content-between mb-3">
                  <span>Best day:</span>
                  <span class="text-success">6 breaks (Tuesday)</span>
                </div>
                <div class="d-flex justify-content-between">
                  <span>Improvement:</span>
                  <span class="text-success">-15% from last week</span>
                </div>
              </div>
            </div>

            <!-- Daily Breakdown -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Daily Breakdown</h3>
              </div>
              <div class="card-body">
                <div class="daily-stats">
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Today</span>
                    <div class="d-flex align-items-center gap-2">
                      <div class="progress" style="width: 100px;">
                        <div class="progress-bar" style="width: 33%;"></div>
                      </div>
                      <span>4/12</span>
                    </div>
                  </div>
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Yesterday</span>
                    <div class="d-flex align-items-center gap-2">
                      <div class="progress" style="width: 100px;">
                        <div class="progress-bar success" style="width: 75%;"></div>
                      </div>
                      <span>9/12</span>
                    </div>
                  </div>
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>2 days ago</span>
                    <div class="d-flex align-items-center gap-2">
                      <div class="progress" style="width: 100px;">
                        <div class="progress-bar" style="width: 58%;"></div>
                      </div>
                      <span>7/12</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Achievements -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Achievements</h3>
              </div>
              <div class="card-body">
                <div class="alert alert-success">
                  🎉 <strong>3 Day Streak!</strong> You've stayed within your limit for 3 days.
                </div>
                <div class="alert alert-info">
                  📈 <strong>Progress Milestone:</strong> 25% reduction from your starting point.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Settings Page -->
        <div class="page" id="settings-page" style="display: none;">
          <div class="container">
            <div class="text-center mb-4">
              <h1>Settings</h1>
              <p class="text-secondary">Customize your quit plan</p>
            </div>

            <!-- Current Plan -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Current Plan</h3>
              </div>
              <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                  <span>Daily breaks:</span>
                  <span class="text-primary" id="settings-daily-breaks">12</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span>Cessation speed:</span>
                  <span class="text-primary" id="settings-cessation-speed">Normal (60 days)</span>
                </div>
                <div class="d-flex justify-content-between">
                  <span>Days completed:</span>
                  <span class="text-primary" id="settings-days-completed">7</span>
                </div>
              </div>
            </div>

            <!-- Modify Settings -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Modify Settings</h3>
              </div>
              <div class="card-body">
                <div class="form-group">
                  <label class="form-label">Daily Vaping Breaks</label>
                  <input type="number" id="modify-daily-breaks" class="form-control"
                         placeholder="Enter number" min="1" max="100">
                  <small class="text-secondary">Changes will apply from tomorrow</small>
                </div>

                <div class="form-group">
                  <label class="form-label">Cessation Speed</label>
                  <select id="modify-cessation-speed" class="form-control">
                    <option value="fast">Fast (30 days)</option>
                    <option value="normal">Normal (60 days)</option>
                    <option value="relaxed">Relaxed (90 days)</option>
                  </select>
                </div>

                <button class="btn btn-primary btn-block" onclick="saveSettings()">
                  Save Changes
                </button>
              </div>
            </div>

            <!-- Data Management -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Data Management</h3>
              </div>
              <div class="card-body">
                <button class="btn btn-outline btn-block mb-3" onclick="exportData()">
                  Export My Data
                </button>
                <button class="btn btn-outline btn-block mb-3" onclick="importData()">
                  Import Data
                </button>
                <button class="btn btn-accent btn-block" onclick="resetApp()">
                  Reset App (Start Over)
                </button>
              </div>
            </div>

            <!-- App Information -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">About</h3>
              </div>
              <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                  <span>Version:</span>
                  <span>1.0.0</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                  <span>Build:</span>
                  <span>2025.01.001</span>
                </div>
                <div class="text-center mt-3">
                  <p class="text-secondary">
                    ByeVape helps you quit vaping gradually with a supportive tracking system.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

      </main>

      <!-- Bottom Navigation -->
      <nav class="bottom-navigation" id="bottom-nav">
        <div class="nav-item" data-page="tracker">
          <div class="nav-icon">📊</div>
          <div class="nav-label">Tracker</div>
        </div>
        <div class="nav-item" data-page="statistics">
          <div class="nav-icon">📈</div>
          <div class="nav-label">Statistics</div>
        </div>
        <div class="nav-item" data-page="settings">
          <div class="nav-icon">⚙️</div>
          <div class="nav-label">Settings</div>
        </div>
      </nav>
    </div>

  </body>
</html>
