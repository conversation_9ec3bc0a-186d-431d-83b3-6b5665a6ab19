<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <title>ByeVape - Vaping Cessation Support</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="description" content="ByeVape helps you quit vaping gradually with a supportive tracking system" />

    <script
      type="module"
      src="https://unpkg.com/@ionic/pwa-elements@latest/dist/ionicpwaelements/ionicpwaelements.esm.js"
    ></script>
    <script
      nomodule
      src="https://unpkg.com/@ionic/pwa-elements@latest/dist/ionicpwaelements/ionicpwaelements.js"
    ></script>

    <link rel="icon" type="image/x-icon" href="/assets/favicon-UvKAN7pv.ico" />
    <link rel="manifest" href="/assets/manifest-RD-sWVQA.json" />
    <meta name="theme-color" content="#3b2d56" />
    <script type="module" crossorigin src="/assets/index-85x8e9FY.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-DwVueIa3.css">
  </head>
  <body>
    <div class="app-layout">
      <!-- Main Content Area -->
      <main class="main-content">
        <div class="container">
          <!-- Design System Demo -->
          <div class="text-center mb-4">
            <h1>ByeVape</h1>
            <p class="text-secondary">Your journey to freedom starts here</p>
          </div>

          <!-- Demo Card with Typography -->
          <div class="card">
            <div class="card-header">
              <h2 class="card-title">Design System Preview</h2>
            </div>
            <div class="card-body">
              <h3>Typography Showcase</h3>
              <p>This demonstrates our <strong>Source Sans 3</strong> body font with proper spacing and readability.</p>

              <h4>Color Palette</h4>
              <div class="d-flex flex-column gap-2 mb-3">
                <div class="d-flex align-items-center gap-2">
                  <div style="width: 20px; height: 20px; background-color: var(--color-primary); border-radius: 4px;"></div>
                  <span>Primary: #3b2d56</span>
                </div>
                <div class="d-flex align-items-center gap-2">
                  <div style="width: 20px; height: 20px; background-color: var(--color-secondary); border-radius: 4px;"></div>
                  <span>Secondary: #6e588b</span>
                </div>
                <div class="d-flex align-items-center gap-2">
                  <div style="width: 20px; height: 20px; background-color: var(--color-accent); border-radius: 4px;"></div>
                  <span>Accent: #a89fc9</span>
                </div>
              </div>

              <h5>Button Components</h5>
              <div class="d-flex flex-column gap-2 mb-3">
                <button class="btn btn-primary">Primary Button</button>
                <button class="btn btn-secondary">Secondary Button</button>
                <button class="btn btn-accent">Accent Button</button>
                <button class="btn btn-outline">Outline Button</button>
              </div>

              <h6>Progress Example</h6>
              <div class="progress">
                <div class="progress-bar" style="width: 65%;"></div>
              </div>
              <p class="text-center mt-2">65% Progress</p>
            </div>
          </div>

          <!-- Circular Progress Demo -->
          <div class="card text-center">
            <div class="card-body">
              <h3>Circular Progress Component</h3>
              <div class="circular-progress">
                <svg>
                  <circle class="progress-ring" cx="100" cy="100" r="90"></circle>
                  <circle class="progress-ring-fill" cx="100" cy="100" r="90"
                          stroke-dasharray="565.48" stroke-dashoffset="226.19"></circle>
                </svg>
                <div class="progress-text">
                  <div class="progress-time">02:45:30</div>
                  <div class="progress-label">Next Break</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Demo -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Form Components</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label class="form-label">Daily Vaping Breaks</label>
                <input type="number" class="form-control" placeholder="Enter number of breaks" value="12">
              </div>
              <div class="form-group">
                <label class="form-label">Cessation Speed</label>
                <select class="form-control">
                  <option>Fast (30 days)</option>
                  <option selected>Normal (60 days)</option>
                  <option>Relaxed (90 days)</option>
                </select>
              </div>
              <button class="btn btn-primary btn-block">Save Settings</button>
            </div>
          </div>

          <!-- Alert Demo -->
          <div class="alert alert-success">
            <strong>Success!</strong> Your design system is working perfectly.
          </div>

        </div>
      </main>
    </div>

  </body>
</html>
