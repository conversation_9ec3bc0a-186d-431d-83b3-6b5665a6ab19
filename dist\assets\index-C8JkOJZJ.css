@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&family=Source+Sans+3:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap');

/* ===================================
   BYEVAPE DESIGN SYSTEM
   ================================== */
/* Import Google Fonts */
/* ===================================
   CSS CUSTOM PROPERTIES (DESIGN TOKENS)
   ================================== */
:root {
  /* Color Palette */
  --color-primary: #3b2d56;
  --color-secondary: #6e588b;
  --color-accent: #a89fc9;
  --color-light: #f3eefc;
  --color-dark: #181224;

  /* Color Variations */
  --color-primary-light: #4a3a6b;
  --color-primary-dark: #2c1f41;
  --color-secondary-light: #7d669a;
  --color-secondary-dark: #5f4a7c;
  --color-accent-light: #b7aed4;
  --color-accent-dark: #9990be;

  /* Semantic Colors */
  --color-success: #28a745;
  --color-warning: #ffc107;
  --color-error: #dc3545;
  --color-info: #17a2b8;

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray-100: #f8f9fa;
  --color-gray-200: #e9ecef;
  --color-gray-300: #dee2e6;
  --color-gray-400: #ced4da;
  --color-gray-500: #adb5bd;
  --color-gray-600: #6c757d;
  --color-gray-700: #495057;
  --color-gray-800: #343a40;
  --color-gray-900: #212529;

  /* Typography */
  --font-family-heading: 'Playfair Display', serif;
  --font-family-body: 'Source Sans 3', sans-serif;

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  --spacing-4xl: 6rem;     /* 96px */

  /* Border Radius */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius-md: 0.5rem;   /* 8px */
  --border-radius-lg: 1rem;     /* 16px */
  --border-radius-xl: 1.5rem;   /* 24px */
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Z-Index Scale */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Breakpoints (for reference in media queries) */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}
/* ===================================
   BASE STYLES & RESET
   ================================== */
/* Box sizing reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}
/* Remove default margins and paddings */
* {
  margin: 0;
  padding: 0;
}
/* HTML and Body base styles */
html {
  font-size: 16px; /* Base font size for rem calculations */
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body {
  font-family: var(--font-family-body);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-dark);
  background-color: var(--color-light);
  min-height: 100vh;
  overflow-x: hidden;

  /* Mobile-first responsive design */
  padding: 0;
  margin: 0;

  /* Prevent horizontal scroll on mobile */
  width: 100%;
  max-width: 100vw;
}
/* Typography Base Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-semibold);
  font-style: italic;
  line-height: var(--line-height-tight);
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
}
h1 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-lg);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-base);
}
p {
  margin-bottom: var(--spacing-md);
  color: var(--color-dark);
}
/* Links */
a {
  color: var(--color-secondary);
  text-decoration: none;
  transition: color var(--transition-fast);
}
a:hover,
a:focus {
  color: var(--color-primary);
  text-decoration: underline;
}
/* Lists */
ul, ol {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-lg);
}
li {
  margin-bottom: var(--spacing-xs);
}
/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}
/* Buttons base styles */
button {
  font-family: var(--font-family-body);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}
/* Form elements */
input, textarea, select {
  font-family: var(--font-family-body);
  font-size: var(--font-size-base);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: border-color var(--transition-fast);
  width: 100%;
}
input:focus,
textarea:focus,
select:focus {
  outline: none;
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(110, 88, 139, 0.1);
}
/* ===================================
   RESPONSIVE LAYOUT SYSTEM
   ================================== */
/* Container for mobile-first design */
.container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}
/* Mobile container (default) */
@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 720px;
    padding: 0 var(--spacing-lg);
  }
}
@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}
/* App Layout - Portrait Only */
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--color-light);

  /* Force portrait orientation */
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
}
/* Main content area */
.main-content {
  flex: 1;
  padding: var(--spacing-md);
  padding-bottom: calc(var(--spacing-xl) + 60px); /* Account for bottom navigation */
  overflow-y: auto;
}
/* Page wrapper */
.page {
  min-height: calc(100vh - 60px); /* Account for navigation height */
  display: flex;
  flex-direction: column;
}
/* ===================================
   UTILITY CLASSES
   ================================== */
/* Display utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }
/* Flexbox utilities */
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-center { align-items: center !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.flex-1 { flex: 1 !important; }
/* Text utilities */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
/* Text colors */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-secondary) !important; }
.text-accent { color: var(--color-accent) !important; }
.text-light { color: var(--color-light) !important; }
.text-dark { color: var(--color-dark) !important; }
.text-white { color: var(--color-white) !important; }
.text-success { color: var(--color-success) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-error { color: var(--color-error) !important; }
/* Background colors */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-accent { background-color: var(--color-accent) !important; }
.bg-light { background-color: var(--color-light) !important; }
.bg-dark { background-color: var(--color-dark) !important; }
.bg-white { background-color: var(--color-white) !important; }
/* Spacing utilities */
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.ml-2 { margin-left: var(--spacing-sm) !important; }
.mr-2 { margin-right: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.ml-3 { margin-left: var(--spacing-md) !important; }
.mr-3 { margin-right: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.ml-4 { margin-left: var(--spacing-lg) !important; }
.mr-4 { margin-right: var(--spacing-lg) !important; }
.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pl-1 { padding-left: var(--spacing-xs) !important; }
.pr-1 { padding-right: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pl-2 { padding-left: var(--spacing-sm) !important; }
.pr-2 { padding-right: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }
.pl-3 { padding-left: var(--spacing-md) !important; }
.pr-3 { padding-right: var(--spacing-md) !important; }
/* Border radius utilities */
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }
/* Shadow utilities */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
/* ===================================
   COMPONENT STYLES
   ================================== */
/* Button Components */
.btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  min-height: 48px; /* Touch-friendly minimum */
  font-size: var(--font-size-base);
  line-height: 1;
}
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
/* Primary Button */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}
.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}
.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}
/* Secondary Button */
.btn-secondary {
  background-color: var(--color-secondary);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}
.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-secondary-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}
/* Accent Button */
.btn-accent {
  background-color: var(--color-accent);
  color: var(--color-dark);
  box-shadow: var(--shadow-md);
}
.btn-accent:hover:not(:disabled) {
  background-color: var(--color-accent-dark);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}
/* Outline Button */
.btn-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  box-shadow: none;
}
.btn-outline:hover:not(:disabled) {
  background-color: var(--color-primary);
  color: var(--color-white);
}
/* Large Button */
.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 56px;
  border-radius: var(--border-radius-xl);
}
/* Small Button */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 40px;
  border-radius: var(--border-radius-md);
}
/* Full Width Button */
.btn-block {
  width: 100%;
  display: flex;
}
/* Card Component */
.card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  transition: box-shadow var(--transition-fast);
}
.card:hover {
  box-shadow: var(--shadow-lg);
}
.card-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--color-gray-200);
}
.card-title {
  font-family: var(--font-family-heading);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  font-style: italic;
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
}
.card-body {
  flex: 1;
}
.card-footer {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--color-gray-200);
}
/* Progress Bar Component */
.progress {
  width: 100%;
  height: 8px;
  background-color: var(--color-gray-200);
  border-radius: var(--border-radius-full);
  overflow: hidden;
  margin: var(--spacing-sm) 0;
}
.progress-bar {
  height: 100%;
  background-color: var(--color-secondary);
  border-radius: var(--border-radius-full);
  transition: width var(--transition-normal);
}
.progress-bar.success {
  background-color: var(--color-success);
}
.progress-bar.warning {
  background-color: var(--color-warning);
}
.progress-bar.error {
  background-color: var(--color-error);
}
/* Circular Progress Component */
.circular-progress {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
}
.circular-progress svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}
.circular-progress .progress-ring {
  fill: none;
  stroke: var(--color-gray-200);
  stroke-width: 8;
}
.circular-progress .progress-ring-fill {
  fill: none;
  stroke: var(--color-secondary);
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dasharray var(--transition-normal);
}
.circular-progress .progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
}
.circular-progress .progress-time {
  font-size: var(--font-size-2xl);
  line-height: 1;
  margin-bottom: var(--spacing-xs);
}
.circular-progress .progress-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-family: var(--font-family-body);
  font-weight: var(--font-weight-normal);
  font-style: normal;
}
/* Form Components */
.form-group {
  margin-bottom: var(--spacing-lg);
}
.form-label {
  display: block;
  font-weight: var(--font-weight-medium);
  color: var(--color-dark);
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-sm);
}
.form-control {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--color-gray-300);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-fast);
  background-color: var(--color-white);
}
.form-control:focus {
  outline: none;
  border-color: var(--color-secondary);
  box-shadow: 0 0 0 3px rgba(110, 88, 139, 0.1);
}
.form-control.error {
  border-color: var(--color-error);
}
.form-control.success {
  border-color: var(--color-success);
}
/* Alert Components */
.alert {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-md);
  border-left: 4px solid;
}
.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  border-left-color: var(--color-success);
  color: var(--color-success);
}
.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left-color: var(--color-warning);
  color: #856404;
}
.alert-error {
  background-color: rgba(220, 53, 69, 0.1);
  border-left-color: var(--color-error);
  color: var(--color-error);
}
.alert-info {
  background-color: rgba(23, 162, 184, 0.1);
  border-left-color: var(--color-info);
  color: var(--color-info);
}
/* ===================================
   MOBILE-SPECIFIC STYLES
   ================================== */
/* Touch-friendly interactions */
@media (max-width: 768px) {
  /* Increase touch targets */
  .btn {
    min-height: 48px;
    padding: var(--spacing-md) var(--spacing-lg);
  }

  /* Larger text for better readability */
  body {
    font-size: var(--font-size-lg);
  }

  h1 {
    font-size: var(--font-size-3xl);
  }

  h2 {
    font-size: var(--font-size-2xl);
  }

  /* Adjust spacing for mobile */
  .main-content {
    padding: var(--spacing-sm);
  }

  .card {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
  }

  /* Larger circular progress for mobile */
  .circular-progress {
    width: 250px;
    height: 250px;
  }

  .circular-progress .progress-time {
    font-size: var(--font-size-3xl);
  }
}
/* ===================================
   ACCESSIBILITY IMPROVEMENTS
   ================================== */
/* Focus styles for keyboard navigation */
*:focus {
  outline: 2px solid var(--color-secondary);
  outline-offset: 2px;
}
/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #000000;
    --color-secondary: #333333;
    --color-accent: #666666;
    --color-light: #ffffff;
    --color-dark: #000000;
  }
}
/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
  :root {
    --color-light: #1a1a1a;
    --color-dark: #ffffff;
    --color-gray-100: #2d2d2d;
    --color-gray-200: #404040;
    --color-gray-300: #525252;
  }
}
/* ===================================
   NAVIGATION COMPONENTS
   ================================== */
/* Bottom Navigation */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  border-top: 1px solid var(--color-gray-200);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: var(--spacing-sm) 0;
  box-shadow: var(--shadow-lg);
  z-index: var(--z-index-fixed);
  height: 70px;
}
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: var(--border-radius-md);
  min-width: 60px;
  flex: 1;
  max-width: 100px;
}
.nav-item:hover {
  background-color: var(--color-gray-100);
}
.nav-item.active {
  background-color: var(--color-primary);
  color: var(--color-white);
}
.nav-item.active .nav-icon {
  transform: scale(1.1);
}
.nav-icon {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-xs);
  transition: transform var(--transition-fast);
}
.nav-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-align: center;
  line-height: 1;
}
/* Page Management */
.page {
  min-height: calc(100vh - 70px); /* Account for bottom navigation */
  padding-bottom: var(--spacing-xl);
  animation: fadeIn var(--transition-normal) ease-in-out;
}
.page.hidden {
  display: none !important;
}
/* Page Transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}
/* ===================================
   PAGE-SPECIFIC COMPONENTS
   ================================== */
/* Onboarding Styles */
.onboarding-step {
  animation: slideIn var(--transition-normal) ease-in-out;
  min-height: 300px;
  padding: var(--spacing-lg);
}
/* Ensure onboarding page is visible */
#onboarding-page {
  background-color: var(--color-light);
  min-height: 100vh;
  position: relative;
  z-index: 1;
}
#onboarding-page .container {
  padding-top: var(--spacing-2xl);
  padding-bottom: var(--spacing-2xl);
}
/* Debug styles for onboarding */
#onboarding-page.debug {
  background-color: #ff0000 !important;
  border: 5px solid #00ff00 !important;
}
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
.cessation-speed-btn {
  padding: var(--spacing-lg);
  text-align: center;
  min-height: 80px;
}
.cessation-speed-btn.selected {
  background-color: var(--color-primary);
  color: var(--color-white);
  border-color: var(--color-primary);
}
/* Tracker Page Styles */
.circular-progress {
  margin: var(--spacing-lg) auto;
}
.daily-stats .progress {
  height: 6px;
}
/* Statistics Page Styles */
.daily-stats {
  max-height: 300px;
  overflow-y: auto;
}
.daily-stats .d-flex {
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--color-gray-100);
}
.daily-stats .d-flex:last-child {
  border-bottom: none;
}
/* Settings Page Styles */
.form-group small {
  display: block;
  margin-top: var(--spacing-xs);
  font-style: italic;
}
/* ===================================
   RESPONSIVE NAVIGATION
   ================================== */
/* Tablet and larger screens */
@media (min-width: 768px) {
  .bottom-navigation {
    max-width: 400px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    border-left: 1px solid var(--color-gray-200);
    border-right: 1px solid var(--color-gray-200);
  }

  .nav-item {
    min-width: 80px;
  }

  .nav-label {
    font-size: var(--font-size-sm);
  }
}
/* Large screens - center the app */
@media (min-width: 992px) {
  .app-layout {
    max-width: 500px;
    margin: 0 auto;
    box-shadow: var(--shadow-xl);
  }

  .main-content {
    background-color: var(--color-white);
  }
}
/* ===================================
   LOADING AND STATE STYLES
   ================================== */
/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-gray-300);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
/* Disabled state */
.disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}
/* Success state */
.success-state {
  background-color: rgba(40, 167, 69, 0.1);
  border: 2px solid var(--color-success);
  color: var(--color-success);
}
/* Warning state */
.warning-state {
  background-color: rgba(255, 193, 7, 0.1);
  border: 2px solid var(--color-warning);
  color: #856404;
}
/* Error state */
.error-state {
  background-color: rgba(220, 53, 69, 0.1);
  border: 2px solid var(--color-error);
  color: var(--color-error);
}
/* ===================================
   PORTRAIT ORIENTATION LOCK
   ================================== */
/* Force portrait orientation on mobile */
@media screen and (orientation: landscape) and (max-width: 768px) {
  .app-layout::before {
    content: "Please rotate your device to portrait mode for the best experience.";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-primary);
    color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    z-index: 9999;
    padding: var(--spacing-lg);
  }

  .main-content,
  .bottom-navigation {
    display: none;
  }
}
