---------------------------- PROCESS STARTED (14580) for package com.byevape.app ----------------------------
--------- beginning of system
--------- beginning of main
23:17:30.597 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~3Ju3rut2IfYo2DjyUv4-pw==/com.byevape.app-lNNYmq6nBJKwoI_NYME_cw==/base.apk
23:25:35.579 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~5ZFMk_fSwAZ82ikDxDd3oA==/com.byevape.app-qBUcp3fGOL7utGgNFwa1Kw==/base.apk
23:25:35.820 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~5ZFMk_fSwAZ82ikDxDd3oA==/com.byevape.app-qBUcp3fGOL7utGgNFwa1Kw==/base.apk
23:25:35.886 com.byevape.app          I  Late-enabling -Xcheck:jni
23:25:35.922 com.byevape.app          I  Using CollectorTypeCC GC.
23:25:35.943 ActivityThread           D  setConscryptValidator
23:25:35.943 ActivityThread           D  setConscryptValidator - put
23:25:35.993 Compatibil...geReporter  D  Compat change id reported: 171979766; UID 10282; state: ENABLED
23:25:36.000 ziparchive               W  Unable to open '/data/app/~~5ZFMk_fSwAZ82ikDxDd3oA==/com.byevape.app-qBUcp3fGOL7utGgNFwa1Kw==/base.dm': No such file or directory
23:25:36.000 ziparchive               W  Unable to open '/data/app/~~5ZFMk_fSwAZ82ikDxDd3oA==/com.byevape.app-qBUcp3fGOL7utGgNFwa1Kw==/base.dm': No such file or directory
23:25:36.092 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~5ZFMk_fSwAZ82ikDxDd3oA==/com.byevape.app-qBUcp3fGOL7utGgNFwa1Kw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~5ZFMk_fSwAZ82ikDxDd3oA==/com.byevape.app-qBUcp3fGOL7utGgNFwa1Kw==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.byevape.app
23:25:36.115 GraphicsEnvironment      V  ANGLE Developer option for 'com.byevape.app' set to: 'default'
23:25:36.115 GraphicsEnvironment      V  ANGLE GameManagerService for com.byevape.app: false
23:25:36.115 GraphicsEnvironment      V  App is not on the allowlist for updatable production driver.
23:25:36.123 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
23:25:36.131 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
23:25:36.180 AppCompatDelegate        D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
23:25:36.185 OpenGLRenderer           D  RenderThread::requireGlContext()
23:25:36.186 AdrenoGLES-0             I  QUALCOMM build                   : 193b2ee, I593c16c433
                                         Build Date                       : 10/07/21
                                         OpenGL ES Shader Compiler Version: EV031.32.02.10
                                         Local Branch                     : Test-lib-**********
                                         Remote Branch                    : 
                                         Remote Branch                    : 
                                         Reconstruct Branch               : 
23:25:36.186 AdrenoGLES-0             I  Build Config                     : S P 10.0.5 AArch64
23:25:36.186 AdrenoGLES-0             I  Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
23:25:36.191 AdrenoGLES-0             I  PFP: 0x016dd093, ME: 0x00000000
23:25:36.199 OpenGLRenderer           D  RenderThread::setGrContext()
23:25:36.229 DecorView                I  [INFO] isPopOver=false config=true
23:25:36.229 DecorView                I  updateCaptionType: isFloating=false isApplication=true hasWindowDecorCaption=false this=DecorView@df84b86[]
23:25:36.229 DecorView                D  setCaptionType = 0, this = DecorView@df84b86[]
23:25:36.235 DecorView                I  getCurrentDensityDpi: from real metrics. densityDpi=480 msg=resources_loaded
23:25:36.235 DecorView                I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@2edac74
23:25:36.241 com.byevape.app          W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
23:25:36.249 WebViewFactory           I  Loading com.google.android.webview version 137.0.7151.61 (code 715106133)
23:25:36.253 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
23:25:36.254 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
23:25:36.254 com.byevape.app          W  Entry not found
23:25:36.254 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk. target_sdk_version=35, uses_libraries=ALL, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
23:25:36.254 nativeloader             D  Extending system_exposed_libraries: libhumantracking.arcsoft.so:libPortraitDistortionCorrection.arcsoft.so:libPortraitDistortionCorrectionCali.arcsoft.so:libface_landmark.arcsoft.so:libFacialStickerEngine.arcsoft.so:libveengine.arcsoft.so:lib_pet_detection.arcsoft.so:libhigh_res.arcsoft.so:libimage_enhancement.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libsuperresolution.arcsoft.so:libobjectcapture.arcsoft.so:libobjectcapture_jni.arcsoft.so:libobjectcapture_jni.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libVideoClassifier.camera.samsung.so:libtensorflowLite.dynamic_viewing.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtflite2.myfilters.camera.samsung.so:libHIDTSnapJNI.camera.samsung.so:libSmartScan.camera.samsung.so:libRectify.camera.samsung.so:libDocRectifyWrapper.camera.samsung.so:libUltraWideDistortionCorrection.camera.
23:25:36.258 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
23:25:36.285 cr_WVCFactoryProvider    I  version=137.0.7151.61 (715106133) minSdkVersion=29 isBundle=false multiprocess=true packageId=127
23:25:36.305 cr_LibraryLoader         I  Successfully loaded native library
23:25:36.306 cr_CachingUmaRecorder    I  Flushed 7 samples from 7 histograms, 0 samples were dropped.
23:25:36.308 cr_CombinedPProvider     I  #registerProvider() provider:WV.d9@702867d isPolicyCacheEnabled:false policyProvidersSize:0
23:25:36.313 cr_PolicyProvider        I  #setManagerAndSource() 0
23:25:36.333 cr_CombinedPProvider     I  #linkNativeInternal() 1
23:25:36.333 Compatibil...geReporter  D  Compat change id reported: 183155436; UID 10282; state: ENABLED
23:25:36.334 cr_AppResProvider        I  #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
23:25:36.334 cr_PolicyProvider        I  #notifySettingsAvailable() 0
23:25:36.335 cr_CombinedPProvider     I  #onSettingsAvailable() 0
23:25:36.335 cr_CombinedPProvider     I  #flushPolicies()
23:25:36.364 chromium                 W  [WARNING:net/dns/dns_config_service_android.cc:69] Failed to read DnsConfig.
23:25:36.398 Compatibil...geReporter  D  Compat change id reported: 214741472; UID 10282; state: ENABLED
23:25:36.403 Compatibil...geReporter  D  Compat change id reported: 171228096; UID 10282; state: ENABLED
23:25:36.430 Capacitor                D  Starting BridgeActivity
23:25:36.455 Capacitor                D  Registering plugin instance: CapacitorCookies
23:25:36.457 Capacitor                D  Registering plugin instance: WebView
23:25:36.458 Capacitor                D  Registering plugin instance: CapacitorHttp
23:25:36.459 Capacitor                D  Registering plugin instance: Camera
23:25:36.460 Capacitor                D  Registering plugin instance: SplashScreen
23:25:36.471 Capacitor                W  Unable to read file at path public/plugins
23:25:36.474 Capacitor                D  Loading app at https://localhost
23:25:36.521 cr_media                 W  BLUETOOTH_CONNECT permission is missing.
23:25:36.522 cr_media                 W  getBluetoothAdapter() requires BLUETOOTH permission
23:25:36.522 cr_media                 W  registerBluetoothIntentsIfNeeded: Requires BLUETOOTH permission
23:25:36.539 Capacitor                D  App started
23:25:36.546 Capacitor                D  App resumed
23:25:36.547 MSHandlerLifeCycle       I  check: return. pkg=com.byevape.app parent=null callers=com.android.internal.policy.DecorView.setVisibility:4412 android.app.ActivityThread.handleResumeActivity:5476 android.app.servertransaction.ResumeActivityItem.execute:54 android.app.servertransaction.ActivityTransactionItem.execute:45 android.app.servertransaction.TransactionExecutor.executeLifecycleState:176 
23:25:36.547 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[]
23:25:36.553 NativeCust...ncyManager  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
23:25:36.559 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_IME, mFrame=[0,0][0,0], mVisibleFrame=[0,1692][1080,2400], mVisible=false, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.setView:1732
23:25:36.560 ViewRootIm...nActivity]  I  setView = com.android.internal.policy.DecorView@df84b86 TM=true
23:25:36.562 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:25:36.566 Capacitor                D  App paused
23:25:36.566 Capacitor                D  Handling local request: https://localhost/
23:25:36.569 Choreographer            I  Skipped 49 frames!  The application may be doing too much work on its main thread.
23:25:36.583 Compatibil...geReporter  D  Compat change id reported: 193247900; UID 10282; state: ENABLED
23:25:36.588 ViewRootIm...nActivity]  I  performTraversals params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:25:36.589 ViewRootIm...nActivity]  I  performTraversals mFirst=true windowShouldResize=true viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:25:36.613 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
23:25:36.613 BufferQueueProducer      E  Unable to open libpenguin.so: dlopen failed: library "libpenguin.so" not found.
23:25:36.614 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@b23cb61[MainActivity] mNativeObject= 0xb400007b48ea8880 sc.mNativeObject= 0xb400007b48e914e0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
23:25:36.614 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@b23cb61[MainActivity] mNativeObject= 0xb400007b48ea8880 sc.mNativeObject= 0xb400007b48e914e0 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
23:25:36.614 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=24 res=0x3 s={true 0xb400007ba0b54800} ch=true seqId=0
23:25:36.615 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007ba0b54800} hwInitialized=true
23:25:36.616 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
23:25:36.617 OpenGLRenderer           D  eglCreateWindowSurface
23:25:36.623 ViewRootIm...nActivity]  I  stopped(true) old = false
23:25:36.623 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to true
23:25:36.624 Capacitor                D  App stopped
23:25:36.624 Capacitor                D  Saving instance state!
23:25:36.625 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:25:36.631 OpenGLRenderer           D  setSurface called with nullptr
23:25:36.631 OpenGLRenderer           D  setSurface() destroyed EGLSurface
23:25:36.631 OpenGLRenderer           D  destroyEglSurface
23:25:36.650 Capacitor                D  Handling local request: https://localhost/assets/index-DwVueIa3.css
23:25:36.651 Capacitor                D  Handling local request: https://localhost/assets/index-85x8e9FY.js
23:25:36.660 AdrenoVK-0               I  ===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
23:25:36.660 AdrenoVK-0               I  ===== END DUMP OF OVERRIDDEN SETTINGS =====
23:25:36.662 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=pan forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:25:36.670 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)4 dur=8 res=0x2 s={false 0x0} ch=false seqId=0
23:25:36.670 AdrenoVK-0               I  QUALCOMM build          : 193b2ee, I593c16c433
                                         Build Date              : 10/07/21
                                         Shader Compiler Version : EV031.32.02.10
                                         Local Branch            : Test-lib-**********
                                         Remote Branch           : 
                                         Remote Branch           : 
                                         Reconstruct Branch      : 
23:25:36.670 AdrenoVK-0               I  Build Config            : S P 10.0.5 AArch64
23:25:36.671 AdrenoVK-0               I  Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
23:25:36.675 Capacitor/Console        E  File: https://localhost/ - Line 1 - Msg: Uncaught TypeError: Cannot read properties of undefined (reading 'triggerEvent')
23:25:36.679 WindowManager            E  win=Window{bde3c96 u0 com.byevape.app/com.byevape.app.MainActivity} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=4 caller=com.android.server.wm.ActivityRecord.destroySurfaces:6539 com.android.server.wm.ActivityRecord.destroySurfaces:6520 com.android.server.wm.ActivityRecord.notifyAppStopped:6584 com.android.server.wm.ActivityRecord.activityStopped:7172 com.android.server.wm.ActivityClientController.activityStopped:269 android.app.IActivityClientController$Stub.onTransact:613 com.android.server.wm.ActivityClientController.onTransact:147 
23:25:36.680 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = true visible = false
23:25:36.681 CameraManagerGlobal      I  Connecting to camera service
23:25:36.710 VendorTagDescriptor      D  addVendorDescriptor: vendor tag id 14172875900359437128 added
23:25:36.717 CameraManager            I  registerAvailabilityCallback: Is device callback = false
23:25:36.718 CameraManagerGlobal      I  postSingleUpdate device: camera id 0 status STATUS_PRESENT
23:25:36.718 CameraManagerGlobal      I  postSingleUpdate device: camera id 1 status STATUS_PRESENT
23:25:36.719 CameraManagerGlobal      I  postSingleUpdate device: camera id 2 status STATUS_PRESENT
23:25:36.719 CameraManagerGlobal      I  postSingleUpdate device: camera id 3 status STATUS_PRESENT
23:25:36.732 CameraManagerGlobal      I  Camera 0 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.732 CameraManagerGlobal      I  Camera 1 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.733 OpenGLRenderer           D  setSurface called with nullptr
23:25:36.734 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params=null
23:25:36.734 CameraManagerGlobal      I  Camera 2 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.743 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=9 res=0x0 s={false 0x0} ch=false seqId=0
23:25:36.773 CameraManagerGlobal      I  Camera 20 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.774 CameraManagerGlobal      I  Camera 21 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.774 CameraManagerGlobal      I  Camera 23 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.863 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = false visible = true
23:25:36.863 ViewRootIm...nActivity]  I  stopped(false) old = true
23:25:36.863 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to false
23:25:36.864 Capacitor                D  App restarted
23:25:36.864 Capacitor                D  App started
23:25:36.866 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:25:36.866 ViewRootIm...nActivity]  I  stopped(false) old = false
23:25:36.866 Capacitor                D  App resumed
23:25:36.867 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:25:36.869 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:25:36.873 CameraManagerGlobal      I  Camera 3 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client com.samsung.adaptivebrightnessgo API Level 2User Id 0
23:25:36.873 CameraManagerGlobal      I  Camera 4 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.873 CameraManagerGlobal      I  Camera 40 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.874 CameraManagerGlobal      I  Camera 41 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.874 CameraManagerGlobal      I  Camera 52 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:25:36.878 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.relayoutWindow:9802
23:25:36.882 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
23:25:36.882 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@b23cb61[MainActivity] mNativeObject= 0xb400007b48ea8c00 sc.mNativeObject= 0xb400007b48e92660 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
23:25:36.883 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@b23cb61[MainActivity] mNativeObject= 0xb400007b48ea8c00 sc.mNativeObject= 0xb400007b48e92660 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
23:25:36.883 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=13 res=0x3 s={true 0xb400007ba0b64000} ch=true seqId=0
23:25:36.884 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007ba0b64000} hwInitialized=true
23:25:36.884 OpenGLRenderer           D  eglCreateWindowSurface
23:25:36.884 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
23:25:36.923 VideoCapabilities        W  Unsupported mime image/vnd.android.heic
23:25:36.939 AudioCapabilities        W  Unsupported mime audio/x-ape
23:25:36.940 AudioCapabilities        W  Unsupported mime audio/x-ima
23:25:36.940 AudioCapabilities        W  Unsupported mime audio/mpeg-L1
23:25:36.941 AudioCapabilities        W  Unsupported mime audio/mpeg-L2
23:25:36.941 VideoCapabilities        W  Unsupported mime video/mp43
23:25:36.941 VideoCapabilities        W  Unsupported mime video/wvc1
23:25:36.941 VideoCapabilities        W  Unsupported mime video/x-ms-wmv
23:25:36.941 AudioCapabilities        W  Unsupported mime audio/x-ms-wma
23:25:36.942 VideoCapabilities        W  Unsupported mime video/x-ms-wmv7
23:25:36.942 VideoCapabilities        W  Unsupported mime video/x-ms-wmv8
23:25:37.385 Capacitor                D  Handling local request: https://localhost/assets/favicon-UvKAN7pv.ico
23:25:37.395 com.byevape.app          I  Compiler allocated 6133KB to compile void android.view.ViewRootImpl.performTraversals()
23:25:38.779 WindowManager            E  win=Window{fadcf0c u0 Splash Screen com.byevape.app EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.WindowState.onExitAnimationDone:6000 com.android.server.wm.WindowStateAnimator.onAnimationFinished:225 com.android.server.wm.WindowState.onAnimationFinished:6225 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda4.onAnimationFinished:2 com.android.server.wm.SurfaceAnimator.cancelAnimation:394 com.android.server.wm.SurfaceAnimator.cancelAnimation:274 com.android.server.wm.WindowContainer.cancelAnimation:2857 
23:25:42.326 ProfileInstaller         D  Installing profile for com.byevape.app
23:26:07.256 Capacitor                D  App paused
23:26:07.265 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = true visible = false
23:26:07.296 OpenGLRenderer           D  setSurface called with nullptr
23:26:07.296 OpenGLRenderer           D  setSurface() destroyed EGLSurface
23:26:07.296 OpenGLRenderer           D  destroyEglSurface
23:26:07.297 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params=null
23:26:07.310 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=12 res=0x2 s={false 0x0} ch=true seqId=0
23:26:07.312 ViewRootIm...nActivity]  I  stopped(true) old = false
23:26:07.312 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to true
23:26:07.318 Capacitor                D  App stopped
23:26:07.320 Capacitor                D  Saving instance state!
23:26:07.321 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:26:07.323 ViewRootImpl             I  updatePointerIcon pointerType = 1000, calling pid = 14580
23:26:07.323 InputManager             D  setPointerIconType iconId = 1000, callingPid = 14580
23:26:07.334 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=pan forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:26:07.342 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=7 res=0x2 s={false 0x0} ch=false seqId=0
23:26:07.345 WindowManager            E  win=Window{bde3c96 u0 com.byevape.app/com.byevape.app.MainActivity} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.ActivityRecord.destroySurfaces:6539 com.android.server.wm.ActivityRecord.destroySurfaces:6520 com.android.server.wm.ActivityRecord.notifyAppStopped:6584 com.android.server.wm.ActivityRecord.activityStopped:7172 com.android.server.wm.ActivityClientController.activityStopped:269 android.app.IActivityClientController$Stub.onTransact:613 com.android.server.wm.ActivityClientController.onTransact:147 
---------------------------- PROCESS ENDED (14580) for package com.byevape.app ----------------------------
23:30:33.298 OpenGLRenderer           D  setSurface called with nullptr
23:30:33.299 OpenGLRenderer           D  trimMemory(TRIM_MEMORY_COMPLETE)::destroyRenderingContext
23:30:33.299 OpenGLRenderer           D  RenderThread::destroyRenderingContext()
23:30:33.299 OpenGLRenderer           D  RenderThread::setGrContext()
---------------------------- PROCESS STARTED (23466) for package com.byevape.app ----------------------------
23:43:27.186 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/base.apk
23:43:27.478 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/base.apk
23:43:27.533 com.byevape.app          I  Late-enabling -Xcheck:jni
23:43:27.600 com.byevape.app          I  Using CollectorTypeCC GC.
23:43:27.620 ActivityThread           D  setConscryptValidator
23:43:27.620 ActivityThread           D  setConscryptValidator - put
23:43:27.673 Compatibil...geReporter  D  Compat change id reported: 171979766; UID 10282; state: ENABLED
23:43:27.684 ziparchive               W  Unable to open '/data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/base.dm': No such file or directory
23:43:27.684 ziparchive               W  Unable to open '/data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/base.dm': No such file or directory
23:43:27.783 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.byevape.app
23:43:27.796 GraphicsEnvironment      V  ANGLE Developer option for 'com.byevape.app' set to: 'default'
23:43:27.796 GraphicsEnvironment      V  ANGLE GameManagerService for com.byevape.app: false
23:43:27.797 GraphicsEnvironment      V  App is not on the allowlist for updatable production driver.
23:43:27.801 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
23:43:27.810 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
23:43:27.843 OpenGLRenderer           D  RenderThread::requireGlContext()
23:43:27.854 AdrenoGLES-0             I  QUALCOMM build                   : 193b2ee, I593c16c433
                                         Build Date                       : 10/07/21
                                         OpenGL ES Shader Compiler Version: EV031.32.02.10
                                         Local Branch                     : Test-lib-**********
                                         Remote Branch                    : 
                                         Remote Branch                    : 
                                         Reconstruct Branch               : 
23:43:27.854 AdrenoGLES-0             I  Build Config                     : S P 10.0.5 AArch64
23:43:27.854 AdrenoGLES-0             I  Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
23:43:27.871 AdrenoGLES-0             I  PFP: 0x016dd093, ME: 0x00000000
23:43:27.872 AppCompatDelegate        D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
23:43:27.896 OpenGLRenderer           D  RenderThread::setGrContext()
23:43:27.903 DecorView                I  [INFO] isPopOver=false config=true
23:43:27.903 DecorView                I  updateCaptionType: isFloating=false isApplication=true hasWindowDecorCaption=false this=DecorView@df84b86[]
23:43:27.903 DecorView                D  setCaptionType = 0, this = DecorView@df84b86[]
23:43:27.909 DecorView                I  getCurrentDensityDpi: from real metrics. densityDpi=480 msg=resources_loaded
23:43:27.909 DecorView                I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@2edac74
23:43:27.915 com.byevape.app          W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
23:43:27.929 WebViewFactory           I  Loading com.google.android.webview version 137.0.7151.61 (code 715106133)
23:43:27.932 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
23:43:27.932 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
23:43:27.932 com.byevape.app          W  Entry not found
23:43:27.933 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk. target_sdk_version=35, uses_libraries=ALL, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
23:43:27.933 nativeloader             D  Extending system_exposed_libraries: libhumantracking.arcsoft.so:libPortraitDistortionCorrection.arcsoft.so:libPortraitDistortionCorrectionCali.arcsoft.so:libface_landmark.arcsoft.so:libFacialStickerEngine.arcsoft.so:libveengine.arcsoft.so:lib_pet_detection.arcsoft.so:libhigh_res.arcsoft.so:libimage_enhancement.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libsuperresolution.arcsoft.so:libobjectcapture.arcsoft.so:libobjectcapture_jni.arcsoft.so:libobjectcapture_jni.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libVideoClassifier.camera.samsung.so:libtensorflowLite.dynamic_viewing.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtflite2.myfilters.camera.samsung.so:libHIDTSnapJNI.camera.samsung.so:libSmartScan.camera.samsung.so:libRectify.camera.samsung.so:libDocRectifyWrapper.camera.samsung.so:libUltraWideDistortionCorrection.camera.
23:43:27.939 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
23:43:27.966 cr_WVCFactoryProvider    I  version=137.0.7151.61 (715106133) minSdkVersion=29 isBundle=false multiprocess=true packageId=127
23:43:27.988 chromium                 I  [0608/234327.987804:INFO:android_webview/browser/variations/variations_seed_loader.cc:67] Failed to open file for reading.: No such file or directory (2)
23:43:27.989 cr_LibraryLoader         I  Successfully loaded native library
23:43:27.990 cr_CachingUmaRecorder    I  Flushed 7 samples from 7 histograms, 0 samples were dropped.
23:43:27.993 cr_CombinedPProvider     I  #registerProvider() provider:WV.d9@702867d isPolicyCacheEnabled:false policyProvidersSize:0
23:43:27.995 cr_PolicyProvider        I  #setManagerAndSource() 0
23:43:28.011 cr_CombinedPProvider     I  #linkNativeInternal() 1
23:43:28.011 Compatibil...geReporter  D  Compat change id reported: 183155436; UID 10282; state: ENABLED
23:43:28.012 cr_AppResProvider        I  #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
23:43:28.012 cr_PolicyProvider        I  #notifySettingsAvailable() 0
23:43:28.012 cr_CombinedPProvider     I  #onSettingsAvailable() 0
23:43:28.012 cr_CombinedPProvider     I  #flushPolicies()
23:43:28.038 chromium                 W  [WARNING:net/dns/dns_config_service_android.cc:69] Failed to read DnsConfig.
23:43:28.057 Compatibil...geReporter  D  Compat change id reported: 214741472; UID 10282; state: ENABLED
23:43:28.060 Compatibil...geReporter  D  Compat change id reported: 171228096; UID 10282; state: ENABLED
23:43:28.093 Capacitor                D  Starting BridgeActivity
23:43:28.112 Capacitor                D  Registering plugin instance: CapacitorCookies
23:43:28.115 Capacitor                D  Registering plugin instance: WebView
23:43:28.116 Capacitor                D  Registering plugin instance: CapacitorHttp
23:43:28.117 Capacitor                D  Registering plugin instance: Camera
23:43:28.118 Capacitor                D  Registering plugin instance: SplashScreen
23:43:28.128 Capacitor                W  Unable to read file at path public/plugins
23:43:28.132 Capacitor                D  Loading app at https://localhost
23:43:28.179 cr_media                 W  BLUETOOTH_CONNECT permission is missing.
23:43:28.180 cr_media                 W  getBluetoothAdapter() requires BLUETOOTH permission
23:43:28.180 cr_media                 W  registerBluetoothIntentsIfNeeded: Requires BLUETOOTH permission
23:43:28.205 Capacitor                D  App started
23:43:28.208 Capacitor                D  App resumed
23:43:28.209 MSHandlerLifeCycle       I  check: return. pkg=com.byevape.app parent=null callers=com.android.internal.policy.DecorView.setVisibility:4412 android.app.ActivityThread.handleResumeActivity:5476 android.app.servertransaction.ResumeActivityItem.execute:54 android.app.servertransaction.ActivityTransactionItem.execute:45 android.app.servertransaction.TransactionExecutor.executeLifecycleState:176 
23:43:28.209 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[]
23:43:28.219 NativeCust...ncyManager  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
23:43:28.234 AdrenoVK-0               I  ===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
23:43:28.234 AdrenoVK-0               I  ===== END DUMP OF OVERRIDDEN SETTINGS =====
23:43:28.236 AdrenoVK-0               I  QUALCOMM build          : 193b2ee, I593c16c433
                                         Build Date              : 10/07/21
                                         Shader Compiler Version : EV031.32.02.10
                                         Local Branch            : Test-lib-**********
                                         Remote Branch           : 
                                         Remote Branch           : 
                                         Reconstruct Branch      : 
23:43:28.236 AdrenoVK-0               I  Build Config            : S P 10.0.5 AArch64
23:43:28.236 AdrenoVK-0               I  Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
23:43:28.247 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.setView:1732
23:43:28.250 ViewRootIm...nActivity]  I  setView = com.android.internal.policy.DecorView@df84b86 TM=true
23:43:28.251 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:43:28.254 Capacitor                D  Handling local request: https://localhost/
23:43:28.255 Choreographer            I  Skipped 50 frames!  The application may be doing too much work on its main thread.
23:43:28.266 Compatibil...geReporter  D  Compat change id reported: 193247900; UID 10282; state: ENABLED
23:43:28.271 ViewRootIm...nActivity]  I  performTraversals params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:43:28.272 ViewRootIm...nActivity]  I  performTraversals mFirst=true windowShouldResize=true viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:43:28.280 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
23:43:28.281 BufferQueueProducer      E  Unable to open libpenguin.so: dlopen failed: library "libpenguin.so" not found.
23:43:28.281 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@b23cb61[MainActivity] mNativeObject= 0xb400007b48bd6680 sc.mNativeObject= 0xb400007b48c56340 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
23:43:28.281 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@b23cb61[MainActivity] mNativeObject= 0xb400007b48bd6680 sc.mNativeObject= 0xb400007b48c56340 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
23:43:28.281 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=8 res=0x3 s={true 0xb400007b48bfa800} ch=true seqId=0
23:43:28.282 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007b48bfa800} hwInitialized=true
23:43:28.284 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
23:43:28.284 OpenGLRenderer           D  eglCreateWindowSurface
23:43:28.321 CameraManagerGlobal      I  Connecting to camera service
23:43:28.324 VendorTagDescriptor      D  addVendorDescriptor: vendor tag id 14172875900359437128 added
23:43:28.335 CameraManager            I  registerAvailabilityCallback: Is device callback = false
23:43:28.335 CameraManagerGlobal      I  Camera 0 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.335 CameraManagerGlobal      I  postSingleUpdate device: camera id 0 status STATUS_PRESENT
23:43:28.335 CameraManagerGlobal      I  postSingleUpdate device: camera id 1 status STATUS_PRESENT
23:43:28.335 CameraManagerGlobal      I  postSingleUpdate device: camera id 2 status STATUS_PRESENT
23:43:28.335 CameraManagerGlobal      I  postSingleUpdate device: camera id 3 status STATUS_PRESENT
23:43:28.336 CameraManagerGlobal      I  Camera 1 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.336 CameraManagerGlobal      I  Camera 2 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.336 CameraManagerGlobal      I  Camera 20 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.336 CameraManagerGlobal      I  Camera 21 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.336 CameraManagerGlobal      I  Camera 23 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.336 CameraManagerGlobal      I  Camera 3 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client com.samsung.adaptivebrightnessgo API Level 2User Id 0
23:43:28.336 CameraManagerGlobal      I  Camera 4 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.337 CameraManagerGlobal      I  Camera 40 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.337 CameraManagerGlobal      I  Camera 41 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.337 CameraManagerGlobal      I  Camera 52 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:43:28.403 Capacitor                D  Handling local request: https://localhost/assets/index-BzrPJEGE.js
23:43:28.403 Capacitor                D  Handling local request: https://localhost/assets/index-Br-9nj1h.css
23:43:28.415 VideoCapabilities        W  Unsupported mime image/vnd.android.heic
23:43:28.422 AudioCapabilities        W  Unsupported mime audio/x-ape
23:43:28.423 AudioCapabilities        W  Unsupported mime audio/x-ima
23:43:28.423 AudioCapabilities        W  Unsupported mime audio/mpeg-L1
23:43:28.423 AudioCapabilities        W  Unsupported mime audio/mpeg-L2
23:43:28.423 VideoCapabilities        W  Unsupported mime video/mp43
23:43:28.424 VideoCapabilities        W  Unsupported mime video/wvc1
23:43:28.424 VideoCapabilities        W  Unsupported mime video/x-ms-wmv
23:43:28.424 AudioCapabilities        W  Unsupported mime audio/x-ms-wma
23:43:28.424 VideoCapabilities        W  Unsupported mime video/x-ms-wmv7
23:43:28.424 VideoCapabilities        W  Unsupported mime video/x-ms-wmv8
23:43:28.676 Capacitor                D  Handling local request: https://localhost/assets/favicon-UvKAN7pv.ico
23:43:28.740 com.byevape.app          I  Compiler allocated 6133KB to compile void android.view.ViewRootImpl.performTraversals()
23:43:33.343 ProfileInstaller         D  Installing profile for com.byevape.app
23:43:51.963 Capacitor                D  App paused
23:43:51.998 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = true visible = false
23:43:52.010 OpenGLRenderer           D  setSurface called with nullptr
23:43:52.011 OpenGLRenderer           D  setSurface() destroyed EGLSurface
23:43:52.011 OpenGLRenderer           D  destroyEglSurface
23:43:52.013 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params=null
23:43:52.027 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=13 res=0x2 s={false 0x0} ch=true seqId=0
23:43:52.037 ViewRootImpl             I  updatePointerIcon pointerType = 1000, calling pid = 23466
23:43:52.037 InputManager             D  setPointerIconType iconId = 1000, callingPid = 23466
23:43:52.041 ViewRootIm...nActivity]  I  stopped(true) old = false
23:43:52.042 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to true
23:43:52.043 Capacitor                D  App stopped
23:43:52.044 Capacitor                D  Saving instance state!
23:43:52.046 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:43:52.054 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=pan forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:43:52.061 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=7 res=0x2 s={false 0x0} ch=false seqId=0
23:43:52.062 WindowManager            E  win=Window{6e61a28 u0 com.byevape.app/com.byevape.app.MainActivity} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.ActivityRecord.destroySurfaces:6539 com.android.server.wm.ActivityRecord.destroySurfaces:6520 com.android.server.wm.ActivityRecord.notifyAppStopped:6584 com.android.server.wm.ActivityRecord.activityStopped:7172 com.android.server.wm.ActivityClientController.activityStopped:269 android.app.IActivityClientController$Stub.onTransact:613 com.android.server.wm.ActivityClientController.onTransact:147 
23:43:54.098 WindowManager            E  win=Window{8fe95d u0 Splash Screen com.byevape.app} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.WindowManagerService.tryStartExitingAnimation:3016 com.android.server.wm.WindowManagerService.relayoutWindow:2727 com.android.server.wm.Session.relayout:274 android.view.IWindowSession$Stub.onTransact:779 com.android.server.wm.Session.onTransact:188 android.os.Binder.execTransactInternal:1321 android.os.Binder.execTransact:1280 
23:43:54.281 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = false visible = true
23:43:54.290 ViewRootIm...nActivity]  I  stopped(false) old = true
23:43:54.290 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to false
23:43:54.291 Capacitor                D  App restarted
23:43:54.292 Capacitor                D  App started
23:43:54.295 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:43:54.296 ViewRootIm...nActivity]  I  stopped(false) old = false
23:43:54.297 Capacitor                D  App resumed
23:43:54.299 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@df84b86[MainActivity]
23:43:54.306 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:43:54.314 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=false, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.relayoutWindow:9802
23:43:54.315 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
23:43:54.316 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@b23cb61[MainActivity] mNativeObject= 0xb400007b48bd6680 sc.mNativeObject= 0xb400007b48c562a0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
23:43:54.316 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@b23cb61[MainActivity] mNativeObject= 0xb400007b48bd6680 sc.mNativeObject= 0xb400007b48c562a0 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
23:43:54.316 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=9 res=0x3 s={true 0xb400007b48bfa800} ch=true seqId=0
23:43:54.318 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007b48bfa800} hwInitialized=true
23:43:54.318 OpenGLRenderer           D  eglCreateWindowSurface
23:43:54.322 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
23:43:54.328 ViewRootIm...nActivity]  I  Resizing android.view.ViewRootImpl@9665512: frame = [0,0][1080,2400] reportDraw = true forceLayout = false syncSeqId = 0
23:43:54.346 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6727
23:43:54.346 ViewRootIm...nActivity]  I  handleResized, msg = 5 frames=ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0] parentClippedByDisplayCutout=false} forceNextWindowRelayout=false displayId=0 resizeMode=-1 frameChanged=false displayFrameChanged=false configChanged=false displayChanged=false
23:43:54.347 ViewRootIm...nActivity]  I  handleResized mSyncSeqId = 0
23:43:54.347 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.handleResized:2450 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:6728 android.view.ViewRootImpl$ViewRootHandler.handleMessage:6697 android.os.Handler.dispatchMessage:106 
---------------------------- PROCESS ENDED (23466) for package com.byevape.app ----------------------------
---------------------------- PROCESS STARTED (24484) for package com.byevape.app ----------------------------
23:44:10.471 Compatibil...geReporter  D  Compat change id reported: 171979766; UID 10282; state: ENABLED
23:44:10.480 ziparchive               W  Unable to open '/data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/base.dm': No such file or directory
23:44:10.480 ziparchive               W  Unable to open '/data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/base.dm': No such file or directory
23:44:10.563 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~c0EsknyLjEi_s9423ghh3g==/com.byevape.app-nKJwzXZFacO5_4bcJbim6Q==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.byevape.app
23:44:10.573 GraphicsEnvironment      V  ANGLE Developer option for 'com.byevape.app' set to: 'default'
23:44:10.573 GraphicsEnvironment      V  ANGLE GameManagerService for com.byevape.app: false
23:44:10.573 GraphicsEnvironment      V  App is not on the allowlist for updatable production driver.
23:44:10.577 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
23:44:10.583 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
23:44:10.601 OpenGLRenderer           D  RenderThread::requireGlContext()
23:44:10.601 AdrenoGLES-0             I  QUALCOMM build                   : 193b2ee, I593c16c433
                                         Build Date                       : 10/07/21
                                         OpenGL ES Shader Compiler Version: EV031.32.02.10
                                         Local Branch                     : Test-lib-**********
                                         Remote Branch                    : 
                                         Remote Branch                    : 
                                         Reconstruct Branch               : 
23:44:10.601 AdrenoGLES-0             I  Build Config                     : S P 10.0.5 AArch64
23:44:10.601 AdrenoGLES-0             I  Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
23:44:10.604 AdrenoGLES-0             I  PFP: 0x016dd093, ME: 0x00000000
23:44:10.609 OpenGLRenderer           D  RenderThread::setGrContext()
23:44:10.618 AppCompatDelegate        D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
23:44:10.639 DecorView                I  [INFO] isPopOver=false config=true
23:44:10.639 DecorView                I  updateCaptionType: isFloating=false isApplication=true hasWindowDecorCaption=false this=DecorView@97455e[]
23:44:10.639 DecorView                D  setCaptionType = 0, this = DecorView@97455e[]
23:44:10.643 DecorView                I  getCurrentDensityDpi: from real metrics. densityDpi=480 msg=resources_loaded
23:44:10.643 DecorView                I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@31e160c
23:44:10.647 com.byevape.app          W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
23:44:10.652 WebViewFactory           I  Loading com.google.android.webview version 137.0.7151.61 (code 715106133)
23:44:10.654 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
23:44:10.654 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
23:44:10.654 com.byevape.app          W  Entry not found
23:44:10.655 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk. target_sdk_version=35, uses_libraries=ALL, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
23:44:10.655 nativeloader             D  Extending system_exposed_libraries: libhumantracking.arcsoft.so:libPortraitDistortionCorrection.arcsoft.so:libPortraitDistortionCorrectionCali.arcsoft.so:libface_landmark.arcsoft.so:libFacialStickerEngine.arcsoft.so:libveengine.arcsoft.so:lib_pet_detection.arcsoft.so:libhigh_res.arcsoft.so:libimage_enhancement.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libsuperresolution.arcsoft.so:libobjectcapture.arcsoft.so:libobjectcapture_jni.arcsoft.so:libobjectcapture_jni.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libVideoClassifier.camera.samsung.so:libtensorflowLite.dynamic_viewing.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtflite2.myfilters.camera.samsung.so:libHIDTSnapJNI.camera.samsung.so:libSmartScan.camera.samsung.so:libRectify.camera.samsung.so:libDocRectifyWrapper.camera.samsung.so:libUltraWideDistortionCorrection.camera.
23:44:10.658 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
23:44:10.669 cr_WVCFactoryProvider    I  version=137.0.7151.61 (715106133) minSdkVersion=29 isBundle=false multiprocess=true packageId=127
23:44:10.676 chromium                 I  [0608/234410.676515:INFO:android_webview/browser/variations/variations_seed_loader.cc:67] Failed to open file for reading.: No such file or directory (2)
23:44:10.686 cr_LibraryLoader         I  Successfully loaded native library
23:44:10.686 cr_CachingUmaRecorder    I  Flushed 7 samples from 7 histograms, 0 samples were dropped.
23:44:10.688 cr_CombinedPProvider     I  #registerProvider() provider:WV.d9@742563b isPolicyCacheEnabled:false policyProvidersSize:0
23:44:10.689 cr_PolicyProvider        I  #setManagerAndSource() 0
23:44:10.700 cr_CombinedPProvider     I  #linkNativeInternal() 1
23:44:10.700 Compatibil...geReporter  D  Compat change id reported: 183155436; UID 10282; state: ENABLED
23:44:10.701 cr_AppResProvider        I  #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
23:44:10.701 cr_PolicyProvider        I  #notifySettingsAvailable() 0
23:44:10.701 cr_CombinedPProvider     I  #onSettingsAvailable() 0
23:44:10.701 cr_CombinedPProvider     I  #flushPolicies()
23:44:10.716 chromium                 W  [WARNING:net/dns/dns_config_service_android.cc:69] Failed to read DnsConfig.
23:44:10.745 Compatibil...geReporter  D  Compat change id reported: 214741472; UID 10282; state: ENABLED
23:44:10.749 Compatibil...geReporter  D  Compat change id reported: 171228096; UID 10282; state: ENABLED
23:44:10.770 Capacitor                D  Starting BridgeActivity
23:44:10.783 Capacitor                D  Registering plugin instance: CapacitorCookies
23:44:10.785 Capacitor                D  Registering plugin instance: WebView
23:44:10.786 Capacitor                D  Registering plugin instance: CapacitorHttp
23:44:10.787 Capacitor                D  Registering plugin instance: Camera
23:44:10.788 Capacitor                D  Registering plugin instance: SplashScreen
23:44:10.796 Capacitor                W  Unable to read file at path public/plugins
23:44:10.798 Capacitor                D  Loading app at https://localhost
23:44:10.809 cr_media                 W  BLUETOOTH_CONNECT permission is missing.
23:44:10.809 cr_media                 W  getBluetoothAdapter() requires BLUETOOTH permission
23:44:10.809 cr_media                 W  registerBluetoothIntentsIfNeeded: Requires BLUETOOTH permission
23:44:10.821 AdrenoVK-0               I  ===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
23:44:10.821 AdrenoVK-0               I  ===== END DUMP OF OVERRIDDEN SETTINGS =====
23:44:10.821 AdrenoVK-0               I  QUALCOMM build          : 193b2ee, I593c16c433
                                         Build Date              : 10/07/21
                                         Shader Compiler Version : EV031.32.02.10
                                         Local Branch            : Test-lib-**********
                                         Remote Branch           : 
                                         Remote Branch           : 
                                         Reconstruct Branch      : 
23:44:10.821 AdrenoVK-0               I  Build Config            : S P 10.0.5 AArch64
23:44:10.821 AdrenoVK-0               I  Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
23:44:10.822 Capacitor                D  App started
23:44:10.823 Capacitor                D  App resumed
23:44:10.824 MSHandlerLifeCycle       I  check: return. pkg=com.byevape.app parent=null callers=com.android.internal.policy.DecorView.setVisibility:4412 android.app.ActivityThread.handleResumeActivity:5476 android.app.servertransaction.ResumeActivityItem.execute:54 android.app.servertransaction.ActivityTransactionItem.execute:45 android.app.servertransaction.TransactionExecutor.executeLifecycleState:176 
23:44:10.825 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@97455e[]
23:44:10.829 NativeCust...ncyManager  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
23:44:10.837 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.setView:1732
23:44:10.839 ViewRootIm...nActivity]  I  setView = com.android.internal.policy.DecorView@97455e TM=true
23:44:10.840 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@97455e[MainActivity]
23:44:10.842 Capacitor                D  Handling local request: https://localhost/
23:44:10.850 Choreographer            I  Skipped 30 frames!  The application may be doing too much work on its main thread.
23:44:10.856 VideoCapabilities        W  Unsupported mime image/vnd.android.heic
23:44:10.858 Compatibil...geReporter  D  Compat change id reported: 193247900; UID 10282; state: ENABLED
23:44:10.863 CameraManagerGlobal      I  Connecting to camera service
23:44:10.865 AudioCapabilities        W  Unsupported mime audio/x-ape
23:44:10.865 AudioCapabilities        W  Unsupported mime audio/x-ima
23:44:10.865 ViewRootIm...nActivity]  I  performTraversals params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:44:10.866 AudioCapabilities        W  Unsupported mime audio/mpeg-L1
23:44:10.866 AudioCapabilities        W  Unsupported mime audio/mpeg-L2
23:44:10.866 VideoCapabilities        W  Unsupported mime video/mp43
23:44:10.866 ViewRootIm...nActivity]  I  performTraversals mFirst=true windowShouldResize=true viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:44:10.866 VideoCapabilities        W  Unsupported mime video/wvc1
23:44:10.867 VideoCapabilities        W  Unsupported mime video/x-ms-wmv
23:44:10.867 AudioCapabilities        W  Unsupported mime audio/x-ms-wma
23:44:10.867 VideoCapabilities        W  Unsupported mime video/x-ms-wmv7
23:44:10.867 VideoCapabilities        W  Unsupported mime video/x-ms-wmv8
23:44:10.868 VendorTagDescriptor      D  addVendorDescriptor: vendor tag id 14172875900359437128 added
23:44:10.873 CameraManagerGlobal      I  Camera 0 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.873 CameraManagerGlobal      I  Camera 1 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.873 CameraManagerGlobal      I  Camera 2 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.873 CameraManagerGlobal      I  Camera 20 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.873 CameraManagerGlobal      I  Camera 21 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.873 CameraManagerGlobal      I  Camera 23 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.874 CameraManagerGlobal      I  Camera 3 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client com.samsung.adaptivebrightnessgo API Level 2User Id 0
23:44:10.874 CameraManagerGlobal      I  Camera 4 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.874 CameraManagerGlobal      I  Camera 40 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.874 CameraManager            I  registerAvailabilityCallback: Is device callback = false
23:44:10.874 CameraManagerGlobal      I  Camera 41 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.874 CameraManagerGlobal      I  postSingleUpdate device: camera id 0 status STATUS_PRESENT
23:44:10.875 CameraManagerGlobal      I  postSingleUpdate device: camera id 1 status STATUS_PRESENT
23:44:10.875 CameraManagerGlobal      I  postSingleUpdate device: camera id 2 status STATUS_PRESENT
23:44:10.875 CameraManagerGlobal      I  postSingleUpdate device: camera id 3 status STATUS_PRESENT
23:44:10.875 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
23:44:10.875 CameraManagerGlobal      I  Camera 52 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:44:10.876 BufferQueueProducer      E  Unable to open libpenguin.so: dlopen failed: library "libpenguin.so" not found.
23:44:10.876 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@4d0b05e[MainActivity] mNativeObject= 0xb400007ba0c34b80 sc.mNativeObject= 0xb400007ba0d4dac0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
23:44:10.876 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@4d0b05e[MainActivity] mNativeObject= 0xb400007ba0c34b80 sc.mNativeObject= 0xb400007ba0d4dac0 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
23:44:10.877 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=9 res=0x3 s={true 0xb400007b48cd1800} ch=true seqId=0
23:44:10.877 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007b48cd1800} hwInitialized=true
23:44:10.879 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
23:44:10.881 OpenGLRenderer           D  eglCreateWindowSurface
23:44:10.899 Capacitor                D  Handling local request: https://localhost/assets/index-Br-9nj1h.css
23:44:10.900 Capacitor                D  Handling local request: https://localhost/assets/index-BzrPJEGE.js
23:44:10.968 Capacitor                D  Handling local request: https://localhost/assets/favicon-UvKAN7pv.ico
23:44:11.332 com.byevape.app          I  Compiler allocated 6133KB to compile void android.view.ViewRootImpl.performTraversals()
23:44:16.227 ProfileInstaller         D  Installing profile for com.byevape.app
23:44:20.491 System                   W  A resource failed to call close. 
23:44:38.719 Capacitor                D  App paused
23:44:38.737 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = true visible = false
23:44:38.758 OpenGLRenderer           D  setSurface called with nullptr
23:44:38.758 OpenGLRenderer           D  setSurface() destroyed EGLSurface
23:44:38.758 OpenGLRenderer           D  destroyEglSurface
23:44:38.760 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params=null
23:44:38.775 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=13 res=0x2 s={false 0x0} ch=true seqId=0
23:44:38.779 ViewRootIm...nActivity]  I  stopped(true) old = false
23:44:38.779 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to true
23:44:38.782 Capacitor                D  App stopped
23:44:38.789 Capacitor                D  Saving instance state!
23:44:38.791 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@97455e[MainActivity]
23:44:38.792 ViewRootImpl             I  updatePointerIcon pointerType = 1000, calling pid = 24484
23:44:38.793 InputManager             D  setPointerIconType iconId = 1000, callingPid = 24484
23:44:38.805 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=pan forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:44:38.814 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=9 res=0x2 s={false 0x0} ch=false seqId=0
23:44:38.816 WindowManager            E  win=Window{f588262 u0 com.byevape.app/com.byevape.app.MainActivity} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.ActivityRecord.destroySurfaces:6539 com.android.server.wm.ActivityRecord.destroySurfaces:6520 com.android.server.wm.ActivityRecord.notifyAppStopped:6584 com.android.server.wm.ActivityRecord.activityStopped:7172 com.android.server.wm.ActivityClientController.activityStopped:269 android.app.IActivityClientController$Stub.onTransact:613 com.android.server.wm.ActivityClientController.onTransact:147 
23:44:40.836 WindowManager            E  win=Window{5ca5ada u0 Splash Screen com.byevape.app} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.WindowManagerService.tryStartExitingAnimation:3016 com.android.server.wm.WindowManagerService.relayoutWindow:2727 com.android.server.wm.Session.relayout:274 android.view.IWindowSession$Stub.onTransact:779 com.android.server.wm.Session.onTransact:188 android.os.Binder.execTransactInternal:1321 android.os.Binder.execTransact:1280 
23:44:50.775 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = false visible = true
23:44:50.783 ViewRootIm...nActivity]  I  stopped(false) old = true
23:44:50.783 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to false
23:44:50.785 Capacitor                D  App restarted
23:44:50.786 Capacitor                D  App started
23:44:50.789 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@97455e[MainActivity]
23:44:50.790 ViewRootIm...nActivity]  I  stopped(false) old = false
23:44:50.791 Capacitor                D  App resumed
23:44:50.796 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@97455e[MainActivity]
23:44:50.806 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:44:50.815 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
23:44:50.816 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@4d0b05e[MainActivity] mNativeObject= 0xb400007ba0c34b80 sc.mNativeObject= 0xb400007ba0d4da20 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
23:44:50.816 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@4d0b05e[MainActivity] mNativeObject= 0xb400007ba0c34b80 sc.mNativeObject= 0xb400007ba0d4da20 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
23:44:50.817 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=8 res=0x3 s={true 0xb400007b48cd1800} ch=true seqId=0
23:44:50.818 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007b48cd1800} hwInitialized=true
23:44:50.818 OpenGLRenderer           D  eglCreateWindowSurface
23:44:50.819 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
23:45:31.107 Capacitor                D  App paused
23:45:31.118 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = true visible = false
23:45:31.136 OpenGLRenderer           D  setSurface called with nullptr
23:45:31.136 OpenGLRenderer           D  setSurface() destroyed EGLSurface
23:45:31.136 OpenGLRenderer           D  destroyEglSurface
23:45:31.137 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params=null
23:45:31.159 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=8 res=0x2 s={false 0x0} ch=true seqId=0
23:45:31.174 ViewRootIm...nActivity]  I  stopped(true) old = false
23:45:31.174 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to true
23:45:31.177 Capacitor                D  App stopped
23:45:31.178 Capacitor                D  Saving instance state!
23:45:31.180 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@97455e[MainActivity]
23:45:31.180 ViewRootImpl             I  updatePointerIcon pointerType = 1000, calling pid = 24484
23:45:31.181 InputManager             D  setPointerIconType iconId = 1000, callingPid = 24484
23:45:31.191 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=pan forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:45:31.199 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=7 res=0x2 s={false 0x0} ch=false seqId=0
23:45:31.200 WindowManager            E  win=Window{f588262 u0 com.byevape.app/com.byevape.app.MainActivity} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.ActivityRecord.destroySurfaces:6539 com.android.server.wm.ActivityRecord.destroySurfaces:6520 com.android.server.wm.ActivityRecord.notifyAppStopped:6584 com.android.server.wm.ActivityRecord.activityStopped:7172 com.android.server.wm.ActivityClientController.activityStopped:269 android.app.IActivityClientController$Stub.onTransact:613 com.android.server.wm.ActivityClientController.onTransact:147 
