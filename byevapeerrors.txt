00:00:33.395 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~uGKhLtbA9uIyGZAAeXjeJw==/com.byevape.app-yKDIADzNqSW_r9Nqbe6yNw==/base.apk
00:00:33.671 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~uGKhLtbA9uIyGZAAeXjeJw==/com.byevape.app-yKDIADzNqSW_r9Nqbe6yNw==/base.apk
---------------------------- PROCESS STARTED (28804) for package com.byevape.app ----------------------------
00:00:33.795 com.byevape.app          I  Late-enabling -Xcheck:jni
00:00:33.863 com.byevape.app          I  Using CollectorTypeCC GC.
00:00:33.883 ActivityThread           D  setConscryptValidator
00:00:33.883 ActivityThread           D  setConscryptValidator - put
00:00:33.927 Compatibil...geReporter  D  Compat change id reported: 171979766; UID 10282; state: ENABLED
00:00:33.936 ziparchive               W  Unable to open '/data/app/~~uGKhLtbA9uIyGZAAeXjeJw==/com.byevape.app-yKDIADzNqSW_r9Nqbe6yNw==/base.dm': No such file or directory
00:00:33.936 ziparchive               W  Unable to open '/data/app/~~uGKhLtbA9uIyGZAAeXjeJw==/com.byevape.app-yKDIADzNqSW_r9Nqbe6yNw==/base.dm': No such file or directory
00:00:34.027 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~uGKhLtbA9uIyGZAAeXjeJw==/com.byevape.app-yKDIADzNqSW_r9Nqbe6yNw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~uGKhLtbA9uIyGZAAeXjeJw==/com.byevape.app-yKDIADzNqSW_r9Nqbe6yNw==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.byevape.app
00:00:34.039 GraphicsEnvironment      V  ANGLE Developer option for 'com.byevape.app' set to: 'default'
00:00:34.040 GraphicsEnvironment      V  ANGLE GameManagerService for com.byevape.app: false
00:00:34.040 GraphicsEnvironment      V  App is not on the allowlist for updatable production driver.
00:00:34.043 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
00:00:34.051 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
00:00:34.118 OpenGLRenderer           D  RenderThread::requireGlContext()
00:00:34.119 AdrenoGLES-0             I  QUALCOMM build                   : 193b2ee, I593c16c433
                                         Build Date                       : 10/07/21
                                         OpenGL ES Shader Compiler Version: EV031.32.02.10
                                         Local Branch                     : Test-lib-**********
                                         Remote Branch                    : 
                                         Remote Branch                    : 
                                         Reconstruct Branch               : 
00:00:34.119 AdrenoGLES-0             I  Build Config                     : S P 10.0.5 AArch64
00:00:34.119 AdrenoGLES-0             I  Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
00:00:34.125 AdrenoGLES-0             I  PFP: 0x016dd093, ME: 0x00000000
00:00:34.133 OpenGLRenderer           D  RenderThread::setGrContext()
00:00:34.137 AppCompatDelegate        D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
00:00:34.212 DecorView                I  [INFO] isPopOver=false config=true
00:00:34.213 DecorView                I  updateCaptionType: isFloating=false isApplication=true hasWindowDecorCaption=false this=DecorView@85845e3[]
00:00:34.213 DecorView                D  setCaptionType = 0, this = DecorView@85845e3[]
00:00:34.218 DecorView                I  getCurrentDensityDpi: from real metrics. densityDpi=480 msg=resources_loaded
00:00:34.218 DecorView                I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@9c14e99
00:00:34.225 com.byevape.app          W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
00:00:34.234 WebViewFactory           I  Loading com.google.android.webview version 137.0.7151.61 (code 715106133)
00:00:34.237 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
00:00:34.237 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
00:00:34.237 com.byevape.app          W  Entry not found
00:00:34.238 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk. target_sdk_version=35, uses_libraries=ALL, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
00:00:34.238 nativeloader             D  Extending system_exposed_libraries: libhumantracking.arcsoft.so:libPortraitDistortionCorrection.arcsoft.so:libPortraitDistortionCorrectionCali.arcsoft.so:libface_landmark.arcsoft.so:libFacialStickerEngine.arcsoft.so:libveengine.arcsoft.so:lib_pet_detection.arcsoft.so:libhigh_res.arcsoft.so:libimage_enhancement.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libsuperresolution.arcsoft.so:libobjectcapture.arcsoft.so:libobjectcapture_jni.arcsoft.so:libobjectcapture_jni.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libVideoClassifier.camera.samsung.so:libtensorflowLite.dynamic_viewing.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtflite2.myfilters.camera.samsung.so:libHIDTSnapJNI.camera.samsung.so:libSmartScan.camera.samsung.so:libRectify.camera.samsung.so:libDocRectifyWrapper.camera.samsung.so:libUltraWideDistortionCorrection.camera.
00:00:34.242 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
00:00:34.263 cr_WVCFactoryProvider    I  version=137.0.7151.61 (715106133) minSdkVersion=29 isBundle=false multiprocess=true packageId=127
00:00:34.283 chromium                 I  [0609/000034.281965:INFO:android_webview/browser/variations/variations_seed_loader.cc:67] Failed to open file for reading.: No such file or directory (2)
00:00:34.292 cr_LibraryLoader         I  Successfully loaded native library
00:00:34.293 cr_CachingUmaRecorder    I  Flushed 7 samples from 7 histograms, 0 samples were dropped.
00:00:34.295 cr_CombinedPProvider     I  #registerProvider() provider:WV.d9@a358a6c isPolicyCacheEnabled:false policyProvidersSize:0
00:00:34.296 cr_PolicyProvider        I  #setManagerAndSource() 0
00:00:34.343 cr_CombinedPProvider     I  #linkNativeInternal() 1
00:00:34.343 Compatibil...geReporter  D  Compat change id reported: 183155436; UID 10282; state: ENABLED
00:00:34.344 cr_AppResProvider        I  #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
00:00:34.344 cr_PolicyProvider        I  #notifySettingsAvailable() 0
00:00:34.344 cr_CombinedPProvider     I  #onSettingsAvailable() 0
00:00:34.344 cr_CombinedPProvider     I  #flushPolicies()
00:00:34.369 chromium                 W  [WARNING:net/dns/dns_config_service_android.cc:69] Failed to read DnsConfig.
00:00:34.411 Compatibil...geReporter  D  Compat change id reported: 214741472; UID 10282; state: ENABLED
00:00:34.415 Compatibil...geReporter  D  Compat change id reported: 171228096; UID 10282; state: ENABLED
00:00:34.443 Capacitor                D  Starting BridgeActivity
00:00:34.461 Capacitor                D  Registering plugin instance: CapacitorCookies
00:00:34.464 Capacitor                D  Registering plugin instance: WebView
00:00:34.465 Capacitor                D  Registering plugin instance: CapacitorHttp
00:00:34.466 Capacitor                D  Registering plugin instance: Camera
00:00:34.467 Capacitor                D  Registering plugin instance: SplashScreen
00:00:34.477 Capacitor                W  Unable to read file at path public/plugins
00:00:34.481 Capacitor                D  Loading app at https://localhost
00:00:34.502 cr_media                 W  BLUETOOTH_CONNECT permission is missing.
00:00:34.503 cr_media                 W  getBluetoothAdapter() requires BLUETOOTH permission
00:00:34.503 cr_media                 W  registerBluetoothIntentsIfNeeded: Requires BLUETOOTH permission
00:00:34.520 Capacitor                D  App started
00:00:34.522 Capacitor                D  App resumed
00:00:34.524 MSHandlerLifeCycle       I  check: return. pkg=com.byevape.app parent=null callers=com.android.internal.policy.DecorView.setVisibility:4412 android.app.ActivityThread.handleResumeActivity:5476 android.app.servertransaction.ResumeActivityItem.execute:54 android.app.servertransaction.ActivityTransactionItem.execute:45 android.app.servertransaction.TransactionExecutor.executeLifecycleState:176 
00:00:34.524 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@85845e3[]
00:00:34.525 AdrenoVK-0               I  ===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
00:00:34.525 AdrenoVK-0               I  ===== END DUMP OF OVERRIDDEN SETTINGS =====
00:00:34.525 AdrenoVK-0               I  QUALCOMM build          : 193b2ee, I593c16c433
                                         Build Date              : 10/07/21
                                         Shader Compiler Version : EV031.32.02.10
                                         Local Branch            : Test-lib-**********
                                         Remote Branch           : 
                                         Remote Branch           : 
                                         Reconstruct Branch      : 
00:00:34.525 AdrenoVK-0               I  Build Config            : S P 10.0.5 AArch64
00:00:34.525 AdrenoVK-0               I  Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
00:00:34.531 NativeCust...ncyManager  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
00:00:34.541 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.setView:1732
00:00:34.543 ViewRootIm...nActivity]  I  setView = com.android.internal.policy.DecorView@85845e3 TM=true
00:00:34.545 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@85845e3[MainActivity]
00:00:34.554 Choreographer            I  Skipped 57 frames!  The application may be doing too much work on its main thread.
00:00:34.554 Capacitor                D  Handling local request: https://localhost/
00:00:34.569 Compatibil...geReporter  D  Compat change id reported: 193247900; UID 10282; state: ENABLED
00:00:34.576 ViewRootIm...nActivity]  I  performTraversals params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
00:00:34.577 ViewRootIm...nActivity]  I  performTraversals mFirst=true windowShouldResize=true viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
00:00:34.582 CameraManagerGlobal      I  Connecting to camera service
00:00:34.586 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
00:00:34.586 BufferQueueProducer      E  Unable to open libpenguin.so: dlopen failed: library "libpenguin.so" not found.
00:00:34.587 VendorTagDescriptor      D  addVendorDescriptor: vendor tag id 14172875900359437128 added
00:00:34.587 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@c69e4e3[MainActivity] mNativeObject= 0xb400007b48eae680 sc.mNativeObject= 0xb400007b48e989c0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
00:00:34.587 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@c69e4e3[MainActivity] mNativeObject= 0xb400007b48eae680 sc.mNativeObject= 0xb400007b48e989c0 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
00:00:34.587 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=9 res=0x3 s={true 0xb400007b48d64800} ch=true seqId=0
00:00:34.588 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007b48d64800} hwInitialized=true
00:00:34.590 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
00:00:34.590 CameraManager            I  registerAvailabilityCallback: Is device callback = false
00:00:34.591 CameraManagerGlobal      I  postSingleUpdate device: camera id 0 status STATUS_PRESENT
00:00:34.592 OpenGLRenderer           D  eglCreateWindowSurface
00:00:34.592 CameraManagerGlobal      I  postSingleUpdate device: camera id 1 status STATUS_PRESENT
00:00:34.592 CameraManagerGlobal      I  postSingleUpdate device: camera id 2 status STATUS_PRESENT
00:00:34.592 CameraManagerGlobal      I  postSingleUpdate device: camera id 3 status STATUS_PRESENT
00:00:34.598 CameraManagerGlobal      I  Camera 0 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.598 CameraManagerGlobal      I  Camera 1 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.598 CameraManagerGlobal      I  Camera 2 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.598 CameraManagerGlobal      I  Camera 20 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.598 CameraManagerGlobal      I  Camera 21 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.599 CameraManagerGlobal      I  Camera 23 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.599 CameraManagerGlobal      I  Camera 3 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client com.samsung.adaptivebrightnessgo API Level 2User Id 0
00:00:34.599 CameraManagerGlobal      I  Camera 4 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.599 CameraManagerGlobal      I  Camera 40 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.599 CameraManagerGlobal      I  Camera 41 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.600 CameraManagerGlobal      I  Camera 52 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
00:00:34.624 VideoCapabilities        W  Unsupported mime image/vnd.android.heic
00:00:34.629 Capacitor                D  Handling local request: https://localhost/assets/index-Bt0eI5z7.js
00:00:34.631 Capacitor                D  Handling local request: https://localhost/assets/index-C8JkOJZJ.css
00:00:34.662 AudioCapabilities        W  Unsupported mime audio/x-ape
00:00:34.662 AudioCapabilities        W  Unsupported mime audio/x-ima
00:00:34.663 AudioCapabilities        W  Unsupported mime audio/mpeg-L1
00:00:34.663 AudioCapabilities        W  Unsupported mime audio/mpeg-L2
00:00:34.663 VideoCapabilities        W  Unsupported mime video/mp43
00:00:34.663 VideoCapabilities        W  Unsupported mime video/wvc1
00:00:34.676 VideoCapabilities        W  Unsupported mime video/x-ms-wmv
00:00:34.676 AudioCapabilities        W  Unsupported mime audio/x-ms-wma
00:00:34.676 VideoCapabilities        W  Unsupported mime video/x-ms-wmv7
00:00:34.676 VideoCapabilities        W  Unsupported mime video/x-ms-wmv8
00:00:34.937 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 55 - Msg: Initializing ByeVape AppState...
00:00:34.937 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 74 - Msg: No saved user data found - first launch
00:00:34.938 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 100 - Msg: 🚀 Starting app - isFirstLaunch: true, startDate: null
00:00:34.938 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 102 - Msg: 👋 First launch detected - showing onboarding
00:00:34.938 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 133 - Msg: 🔄 showPage called with pageId: onboarding
00:00:34.938 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 135 - Msg: 📄 Found 4 pages to hide
00:00:34.939 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 137 - Msg:    Hiding page 0: onboarding-page
00:00:34.939 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 137 - Msg:    Hiding page 1: tracker-page
00:00:34.939 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 137 - Msg:    Hiding page 2: statistics-page
00:00:34.939 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 137 - Msg:    Hiding page 3: settings-page
00:00:34.940 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 141 - Msg: 🎯 Target page element: [object HTMLDivElement]
00:00:34.940 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 144 - Msg: ✅ Showing page: onboarding-page
00:00:34.945 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 146 - Msg: 📊 Page styles - display: block, visibility: visible, opacity: 1
00:00:34.946 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 149 - Msg: 🔴 DEBUG: Added debug class to onboarding page
00:00:34.946 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 177 - Msg: 🧭 Bottom navigation element: [object HTMLElement]
00:00:34.946 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 181 - Msg: 🔒 Navigation hidden for onboarding
00:00:34.946 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 189 - Msg: ✨ showPage completed for: onboarding
00:00:34.947 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 60 - Msg: ByeVape AppState initialized successfully
00:00:34.947 Capacitor/Console        I  File: https://localhost/assets/index-Bt0eI5z7.js - Line 474 - Msg: ByeVape app initialized successfully
00:00:34.956 Capacitor                D  Handling local request: https://localhost/assets/favicon-UvKAN7pv.ico
00:00:35.064 com.byevape.app          I  Compiler allocated 6133KB to compile void android.view.ViewRootImpl.performTraversals()
00:00:39.880 ProfileInstaller         D  Installing profile for com.byevape.app