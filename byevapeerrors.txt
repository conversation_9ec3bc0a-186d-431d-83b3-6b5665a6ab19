23:54:34.300 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~1RzmMdM0EwjYnC-k1ta0bg==/com.byevape.app-quZPxkolEt_R1kjIXpkd6w==/base.apk
23:54:34.427 VerityUtils              E  Failed to measure fs-verity, errno 95: /data/app/~~1RzmMdM0EwjYnC-k1ta0bg==/com.byevape.app-quZPxkolEt_R1kjIXpkd6w==/base.apk
---------------------------- PROCESS STARTED (25992) for package com.byevape.app ----------------------------
23:54:34.794 Compatibil...geReporter  D  Compat change id reported: 171979766; UID 10282; state: ENABLED
23:54:34.802 ziparchive               W  Unable to open '/data/app/~~1RzmMdM0EwjYnC-k1ta0bg==/com.byevape.app-quZPxkolEt_R1kjIXpkd6w==/base.dm': No such file or directory
23:54:34.802 ziparchive               W  Unable to open '/data/app/~~1RzmMdM0EwjYnC-k1ta0bg==/com.byevape.app-quZPxkolEt_R1kjIXpkd6w==/base.dm': No such file or directory
23:54:34.914 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~1RzmMdM0EwjYnC-k1ta0bg==/com.byevape.app-quZPxkolEt_R1kjIXpkd6w==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~1RzmMdM0EwjYnC-k1ta0bg==/com.byevape.app-quZPxkolEt_R1kjIXpkd6w==/lib/arm64, permitted_path=/data:/mnt/expand:/data/user/0/com.byevape.app
23:54:34.940 GraphicsEnvironment      V  ANGLE Developer option for 'com.byevape.app' set to: 'default'
23:54:34.941 GraphicsEnvironment      V  ANGLE GameManagerService for com.byevape.app: false
23:54:34.941 GraphicsEnvironment      V  App is not on the allowlist for updatable production driver.
23:54:34.946 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
23:54:34.955 NetworkSecurityConfig    D  No Network Security Config specified, using platform default
23:54:35.015 OpenGLRenderer           D  RenderThread::requireGlContext()
23:54:35.015 AdrenoGLES-0             I  QUALCOMM build                   : 193b2ee, I593c16c433
                                         Build Date                       : 10/07/21
                                         OpenGL ES Shader Compiler Version: EV031.32.02.10
                                         Local Branch                     : Test-lib-**********
                                         Remote Branch                    : 
                                         Remote Branch                    : 
                                         Reconstruct Branch               : 
23:54:35.015 AdrenoGLES-0             I  Build Config                     : S P 10.0.5 AArch64
23:54:35.015 AdrenoGLES-0             I  Driver Path                      : /vendor/lib64/egl/libGLESv2_adreno.so
23:54:35.022 AdrenoGLES-0             I  PFP: 0x016dd093, ME: 0x00000000
23:54:35.030 OpenGLRenderer           D  RenderThread::setGrContext()
23:54:35.041 AppCompatDelegate        D  Checking for metadata for AppLocalesMetadataHolderService : Service not found
23:54:35.081 DecorView                I  [INFO] isPopOver=false config=true
23:54:35.081 DecorView                I  updateCaptionType: isFloating=false isApplication=true hasWindowDecorCaption=false this=DecorView@85845e3[]
23:54:35.081 DecorView                D  setCaptionType = 0, this = DecorView@85845e3[]
23:54:35.088 DecorView                I  getCurrentDensityDpi: from real metrics. densityDpi=480 msg=resources_loaded
23:54:35.089 DecorView                I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@9c14e99
23:54:35.096 com.byevape.app          W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
23:54:35.108 WebViewFactory           I  Loading com.google.android.webview version 137.0.7151.61 (code 715106133)
23:54:35.111 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
23:54:35.111 ziparchive               W  Unable to open '/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.dm': No such file or directory
23:54:35.112 com.byevape.app          W  Entry not found
23:54:35.112 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk. target_sdk_version=35, uses_libraries=ALL, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
23:54:35.112 nativeloader             D  Extending system_exposed_libraries: libhumantracking.arcsoft.so:libPortraitDistortionCorrection.arcsoft.so:libPortraitDistortionCorrectionCali.arcsoft.so:libface_landmark.arcsoft.so:libFacialStickerEngine.arcsoft.so:libveengine.arcsoft.so:lib_pet_detection.arcsoft.so:libhigh_res.arcsoft.so:libimage_enhancement.arcsoft.so:liblow_light_hdr.arcsoft.so:libhigh_dynamic_range.arcsoft.so:libsuperresolution.arcsoft.so:libobjectcapture.arcsoft.so:libobjectcapture_jni.arcsoft.so:libobjectcapture_jni.arcsoft.so:libFacialAttributeDetection.arcsoft.so:libBeauty_v4.camera.samsung.so:libexifa.camera.samsung.so:libjpega.camera.samsung.so:libOpenCv.camera.samsung.so:libVideoClassifier.camera.samsung.so:libtensorflowLite.dynamic_viewing.camera.samsung.so:libImageScreener.camera.samsung.so:libMyFilter.camera.samsung.so:libtflite2.myfilters.camera.samsung.so:libHIDTSnapJNI.camera.samsung.so:libSmartScan.camera.samsung.so:libRectify.camera.samsung.so:libDocRectifyWrapper.camera.samsung.so:libUltraWideDistortionCorrection.camera.
23:54:35.116 nativeloader             D  Configuring classloader-namespace for other apk /data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk. target_sdk_version=35, uses_libraries=, library_path=/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/lib/arm64:/data/app/~~zIo74cctb5Uphwv_eCRCwQ==/com.google.android.webview-xLy-HK-Ny_yssFItEfYiEw==/base.apk!/lib/arm64-v8a:/data/app/~~NtGuI_aDsubxdIp0wgMxSA==/com.google.android.trichromelibrary_715106133-4yEC51JyPbZJp2oYf1-uOA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand
23:54:35.135 cr_WVCFactoryProvider    I  version=137.0.7151.61 (715106133) minSdkVersion=29 isBundle=false multiprocess=true packageId=127
23:54:35.154 chromium                 I  [0608/235435.150733:INFO:android_webview/browser/variations/variations_seed_loader.cc:67] Failed to open file for reading.: No such file or directory (2)
23:54:35.158 cr_LibraryLoader         I  Successfully loaded native library
23:54:35.159 cr_CachingUmaRecorder    I  Flushed 7 samples from 7 histograms, 0 samples were dropped.
23:54:35.161 cr_CombinedPProvider     I  #registerProvider() provider:WV.d9@9b4671f isPolicyCacheEnabled:false policyProvidersSize:0
23:54:35.166 cr_PolicyProvider        I  #setManagerAndSource() 0
23:54:35.184 cr_CombinedPProvider     I  #linkNativeInternal() 1
23:54:35.184 Compatibil...geReporter  D  Compat change id reported: 183155436; UID 10282; state: ENABLED
23:54:35.185 cr_AppResProvider        I  #getApplicationRestrictionsFromUserManager() Bundle[EMPTY_PARCEL]
23:54:35.185 cr_PolicyProvider        I  #notifySettingsAvailable() 0
23:54:35.185 cr_CombinedPProvider     I  #onSettingsAvailable() 0
23:54:35.185 cr_CombinedPProvider     I  #flushPolicies()
23:54:35.207 chromium                 W  [WARNING:net/dns/dns_config_service_android.cc:69] Failed to read DnsConfig.
23:54:35.245 Compatibil...geReporter  D  Compat change id reported: 214741472; UID 10282; state: ENABLED
23:54:35.250 Compatibil...geReporter  D  Compat change id reported: 171228096; UID 10282; state: ENABLED
23:54:35.279 Capacitor                D  Starting BridgeActivity
23:54:35.295 Capacitor                D  Registering plugin instance: CapacitorCookies
23:54:35.297 Capacitor                D  Registering plugin instance: WebView
23:54:35.298 Capacitor                D  Registering plugin instance: CapacitorHttp
23:54:35.299 Capacitor                D  Registering plugin instance: Camera
23:54:35.300 Capacitor                D  Registering plugin instance: SplashScreen
23:54:35.309 Capacitor                W  Unable to read file at path public/plugins
23:54:35.313 Capacitor                D  Loading app at https://localhost
23:54:35.326 cr_media                 W  BLUETOOTH_CONNECT permission is missing.
23:54:35.326 cr_media                 W  getBluetoothAdapter() requires BLUETOOTH permission
23:54:35.326 cr_media                 W  registerBluetoothIntentsIfNeeded: Requires BLUETOOTH permission
23:54:35.336 AdrenoVK-0               I  ===== BEGIN DUMP OF OVERRIDDEN SETTINGS =====
23:54:35.336 AdrenoVK-0               I  ===== END DUMP OF OVERRIDDEN SETTINGS =====
23:54:35.336 AdrenoVK-0               I  QUALCOMM build          : 193b2ee, I593c16c433
                                         Build Date              : 10/07/21
                                         Shader Compiler Version : EV031.32.02.10
                                         Local Branch            : Test-lib-**********
                                         Remote Branch           : 
                                         Remote Branch           : 
                                         Reconstruct Branch      : 
23:54:35.336 AdrenoVK-0               I  Build Config            : S P 10.0.5 AArch64
23:54:35.336 AdrenoVK-0               I  Driver Path             : /vendor/lib64/hw/vulkan.adreno.so
23:54:35.340 Capacitor                D  App started
23:54:35.343 Capacitor                D  App resumed
23:54:35.344 MSHandlerLifeCycle       I  check: return. pkg=com.byevape.app parent=null callers=com.android.internal.policy.DecorView.setVisibility:4412 android.app.ActivityThread.handleResumeActivity:5476 android.app.servertransaction.ResumeActivityItem.execute:54 android.app.servertransaction.ActivityTransactionItem.execute:45 android.app.servertransaction.TransactionExecutor.executeLifecycleState:176 
23:54:35.344 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@85845e3[]
23:54:35.349 NativeCust...ncyManager  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
23:54:35.357 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_IME, mFrame=[0,0][0,0], mVisibleFrame=[0,1692][1080,2400], mVisible=false, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.setView:1732
23:54:35.359 ViewRootIm...nActivity]  I  setView = com.android.internal.policy.DecorView@85845e3 TM=true
23:54:35.361 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@85845e3[MainActivity]
23:54:35.370 Capacitor                D  App paused
23:54:35.371 Capacitor                D  Handling local request: https://localhost/
23:54:35.372 Choreographer            I  Skipped 46 frames!  The application may be doing too much work on its main thread.
23:54:35.381 Compatibil...geReporter  D  Compat change id reported: 193247900; UID 10282; state: ENABLED
23:54:35.387 ViewRootIm...nActivity]  I  performTraversals params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:54:35.387 ViewRootIm...nActivity]  I  performTraversals mFirst=true windowShouldResize=true viewVisibilityChanged=false mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:54:35.398 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
23:54:35.399 BufferQueueProducer      E  Unable to open libpenguin.so: dlopen failed: library "libpenguin.so" not found.
23:54:35.399 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@c69e4e3[MainActivity] mNativeObject= 0xb400007b48ead480 sc.mNativeObject= 0xb400007bad1605c0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
23:54:35.400 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@c69e4e3[MainActivity] mNativeObject= 0xb400007b48ead480 sc.mNativeObject= 0xb400007bad1605c0 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
23:54:35.400 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=11 res=0x3 s={true 0xb400007b48d62800} ch=true seqId=0
23:54:35.401 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007b48d62800} hwInitialized=true
23:54:35.401 VideoCapabilities        W  Unsupported mime image/vnd.android.heic
23:54:35.402 OpenGLRenderer           D  eglCreateWindowSurface
23:54:35.403 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
23:54:35.416 ViewRootIm...nActivity]  I  stopped(true) old = false
23:54:35.416 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to true
23:54:35.418 Capacitor                D  App stopped
23:54:35.418 Capacitor                D  Saving instance state!
23:54:35.422 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@85845e3[MainActivity]
23:54:35.429 OpenGLRenderer           D  setSurface called with nullptr
23:54:35.429 OpenGLRenderer           D  setSurface() destroyed EGLSurface
23:54:35.429 OpenGLRenderer           D  destroyEglSurface
23:54:35.431 Capacitor                D  Handling local request: https://localhost/assets/index-D6-LEA8s.js
23:54:35.437 Capacitor                D  Handling local request: https://localhost/assets/index-Br-9nj1h.css
23:54:35.469 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=pan forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:54:35.473 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)4 dur=3 res=0x2 s={false 0x0} ch=false seqId=0
23:54:35.478 AudioCapabilities        W  Unsupported mime audio/x-ape
23:54:35.478 WindowManager            E  win=Window{b53c993 u0 com.byevape.app/com.byevape.app.MainActivity} destroySurfaces: appStopped=true cleanupOnResume=false win.mWindowRemovalAllowed=false win.mRemoveOnExit=false win.mViewVisibility=4 caller=com.android.server.wm.ActivityRecord.destroySurfaces:6539 com.android.server.wm.ActivityRecord.destroySurfaces:6520 com.android.server.wm.ActivityRecord.notifyAppStopped:6584 com.android.server.wm.ActivityRecord.activityStopped:7172 com.android.server.wm.ActivityClientController.activityStopped:269 android.app.IActivityClientController$Stub.onTransact:613 com.android.server.wm.ActivityClientController.onTransact:147 
23:54:35.478 AudioCapabilities        W  Unsupported mime audio/x-ima
23:54:35.480 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = true visible = false
23:54:35.515 CameraManagerGlobal      I  Connecting to camera service
23:54:35.528 AudioCapabilities        W  Unsupported mime audio/mpeg-L1
23:54:35.529 AudioCapabilities        W  Unsupported mime audio/mpeg-L2
23:54:35.531 VideoCapabilities        W  Unsupported mime video/mp43
23:54:35.532 VideoCapabilities        W  Unsupported mime video/wvc1
23:54:35.533 VideoCapabilities        W  Unsupported mime video/x-ms-wmv
23:54:35.534 AudioCapabilities        W  Unsupported mime audio/x-ms-wma
23:54:35.534 VideoCapabilities        W  Unsupported mime video/x-ms-wmv7
23:54:35.535 VideoCapabilities        W  Unsupported mime video/x-ms-wmv8
23:54:35.546 VendorTagDescriptor      D  addVendorDescriptor: vendor tag id 14172875900359437128 added
23:54:35.569 CameraManager            I  registerAvailabilityCallback: Is device callback = false
23:54:35.569 CameraManagerGlobal      I  postSingleUpdate device: camera id 0 status STATUS_PRESENT
23:54:35.570 CameraManagerGlobal      I  postSingleUpdate device: camera id 1 status STATUS_PRESENT
23:54:35.570 CameraManagerGlobal      I  postSingleUpdate device: camera id 2 status STATUS_PRESENT
23:54:35.570 CameraManagerGlobal      I  postSingleUpdate device: camera id 3 status STATUS_PRESENT
23:54:35.577 OpenGLRenderer           D  setSurface called with nullptr
23:54:35.578 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params=null
23:54:35.586 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)8 dur=6 res=0x0 s={false 0x0} ch=false seqId=0
23:54:35.593 CameraManagerGlobal      I  Camera 0 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.609 CameraManagerGlobal      I  Camera 1 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.610 CameraManagerGlobal      I  Camera 2 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.610 CameraManagerGlobal      I  Camera 20 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.610 CameraManagerGlobal      I  Camera 21 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.611 CameraManagerGlobal      I  Camera 23 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.611 CameraManagerGlobal      I  Camera 3 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client com.samsung.adaptivebrightnessgo API Level 2User Id 0
23:54:35.611 CameraManagerGlobal      I  Camera 4 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.612 CameraManagerGlobal      I  Camera 40 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.612 CameraManagerGlobal      I  Camera 41 facing CAMERA_FACING_FRONT state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.612 CameraManagerGlobal      I  Camera 52 facing CAMERA_FACING_BACK state now CAMERA_STATE_CLOSED for client android.system API Level 2User Id 0
23:54:35.831 Capacitor/Console        I  File: https://localhost/assets/index-D6-LEA8s.js - Line 55 - Msg: Initializing ByeVape AppState...
23:54:35.833 Capacitor/Console        I  File: https://localhost/assets/index-D6-LEA8s.js - Line 74 - Msg: No saved user data found - first launch
23:54:35.835 Capacitor/Console        I  File: https://localhost/assets/index-D6-LEA8s.js - Line 60 - Msg: ByeVape AppState initialized successfully
23:54:35.836 Capacitor/Console        I  File: https://localhost/assets/index-D6-LEA8s.js - Line 428 - Msg: ByeVape app initialized successfully
23:54:35.915 Capacitor                D  Handling local request: https://localhost/assets/favicon-UvKAN7pv.ico
23:54:36.066 ViewRootIm...nActivity]  I  handleAppVisibility mAppVisible = false visible = true
23:54:36.073 ViewRootIm...nActivity]  I  stopped(false) old = true
23:54:36.073 ViewRootIm...nActivity]  I  WindowStopped on com.byevape.app/com.byevape.app.MainActivity set to false
23:54:36.075 Capacitor                D  App restarted
23:54:36.077 Capacitor                D  App started
23:54:36.079 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@85845e3[MainActivity]
23:54:36.080 ViewRootIm...nActivity]  I  stopped(false) old = false
23:54:36.081 Capacitor                D  App resumed
23:54:36.082 MSHandlerLifeCycle       I  removeMultiSplitHandler: no exist. decor=DecorView@85845e3[MainActivity]
23:54:36.089 ViewRootIm...nActivity]  I  performTraversals mFirst=false windowShouldResize=false viewVisibilityChanged=true mForceNextWindowRelayout=false params={(0,0)(fillxfill) sim={adjust=resize forwardNavigation} layoutInDisplayCutoutMode=shortEdges ty=BASE_APPLICATION wanim=0x1030309
                                           fl=81810100
                                           pfl=12020040
                                           bhv=DEFAULT
                                           fitSides= naviIconColor=0}
23:54:36.098 InsetsController         D  onStateChanged: InsetsState: {mDisplayFrame=Rect(0, 0 - 1080, 2400), mDisplayCutout=DisplayCutout{insets=Rect(0, 88 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 0 - 0, 0), Rect(512, 0 - 568, 88), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2400 physicalDisplayWidth=1080 physicalDisplayHeight=2400 density={3.0} cutoutSpec={M 0,0 M 0,10.71963616907435 a 9.280363830925644,9.280363830925644 0 1,0 0,18.56072766185129 a 9.280363830925644,9.280363830925644 0 1,0 0,-18.56072766185129 Z @dp} rotation={0} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=96, center=Point(96, 96)}, RoundedCorner{position=TopRight, radius=96, center=Point(984, 96)}, RoundedCorner{position=BottomRight, radius=96, center=Point(984, 2304)}, RoundedCorner{position=BottomLeft, radius=96, center=Point(96, 2304)}]}  mRoundedCornerFrame=Rect(0, 0 - 1080, 2400), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(948, 0 - 1080, 88) rotation=0}, mSources= { InsetsSource: {mType=ITYPE_STATUS_BAR, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_NAVIGATION_BAR, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_GESTURES, mFrame=[0,0][0,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_GESTURES, mFrame=[1080,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_MANDATORY_GESTURES, mFrame=[0,0][1080,124], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_MANDATORY_GESTURES, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_LEFT_DISPLAY_CUTOUT, mFrame=[0,0][-100000,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_DISPLAY_CUTOUT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_RIGHT_DISPLAY_CUTOUT, mFrame=[100000,0][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_DISPLAY_CUTOUT, mFrame=[0,100000][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_TOP_TAPPABLE_ELEMENT, mFrame=[0,0][1080,88], mVisible=true, mInsetsRoundedCornerFrame=false}, InsetsSource: {mType=ITYPE_BOTTOM_TAPPABLE_ELEMENT, mFrame=[0,2256][1080,2400], mVisible=true, mInsetsRoundedCornerFrame=false} } host=com.byevape.app/com.byevape.app.MainActivity from=android.view.ViewRootImpl.relayoutWindow:9802
23:54:36.099 ViewRootIm...nActivity]  I  updateBlastSurfaceIfNeeded mBlastBufferQueue=null isSameSurfaceControl=false
23:54:36.100 BLASTBufferQueue         I  new BLASTBufferQueue, mName= ViewRootImpl@c69e4e3[MainActivity] mNativeObject= 0xb400007b48ead800 sc.mNativeObject= 0xb400007ba0d317c0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 android.view.Choreographer.doCallbacks:923 android.view.Choreographer.doFrame:852 android.view.Choreographer$FrameDisplayEventReceiver.run:1283 
23:54:36.100 BLASTBufferQueue         I  update, w= 1080 h= 2400 mName = ViewRootImpl@c69e4e3[MainActivity] mNativeObject= 0xb400007b48ead800 sc.mNativeObject= 0xb400007ba0d317c0 format= -1 caller= android.graphics.BLASTBufferQueue.<init>:84 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:2909 android.view.ViewRootImpl.relayoutWindow:9847 android.view.ViewRootImpl.performTraversals:3884 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 
23:54:36.100 ViewRootIm...nActivity]  I  Relayout returned: old=(0,0,1080,2400) new=(0,0,1080,2400) req=(1080,2400)0 dur=9 res=0x3 s={true 0xb400007bad194000} ch=true seqId=0
23:54:36.101 ViewRootIm...nActivity]  I  mThreadedRenderer.initialize() mSurface={isValid=true 0xb400007bad194000} hwInitialized=true
23:54:36.101 OpenGLRenderer           D  eglCreateWindowSurface
23:54:36.102 ViewRootIm...nActivity]  I  reportNextDraw android.view.ViewRootImpl.performTraversals:4438 android.view.ViewRootImpl.doTraversal:3116 android.view.ViewRootImpl$TraversalRunnable.run:10885 android.view.Choreographer$CallbackRecord.run:1301 android.view.Choreographer$CallbackRecord.run:1309 
23:54:36.481 com.byevape.app          I  Compiler allocated 6133KB to compile void android.view.ViewRootImpl.performTraversals()
23:54:37.568 WindowManager            E  win=Window{81a1494 u0 Splash Screen com.byevape.app EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=false win.mViewVisibility=8 caller=com.android.server.wm.WindowState.onExitAnimationDone:6000 com.android.server.wm.WindowStateAnimator.onAnimationFinished:225 com.android.server.wm.WindowState.onAnimationFinished:6225 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda4.onAnimationFinished:2 com.android.server.wm.SurfaceAnimator.cancelAnimation:394 com.android.server.wm.SurfaceAnimator.cancelAnimation:274 com.android.server.wm.WindowContainer.cancelAnimation:2857 
23:54:40.824 ProfileInstaller         D  Installing profile for com.byevape.app