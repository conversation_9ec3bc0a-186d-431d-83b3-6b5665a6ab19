(function polyfill() {
  const relList = document.createElement("link").relList;
  if (relList && relList.supports && relList.supports("modulepreload")) {
    return;
  }
  for (const link of document.querySelectorAll('link[rel="modulepreload"]')) {
    processPreload(link);
  }
  new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type !== "childList") {
        continue;
      }
      for (const node of mutation.addedNodes) {
        if (node.tagName === "LINK" && node.rel === "modulepreload")
          processPreload(node);
      }
    }
  }).observe(document, { childList: true, subtree: true });
  function getFetchOpts(link) {
    const fetchOpts = {};
    if (link.integrity) fetchOpts.integrity = link.integrity;
    if (link.referrerPolicy) fetchOpts.referrerPolicy = link.referrerPolicy;
    if (link.crossOrigin === "use-credentials")
      fetchOpts.credentials = "include";
    else if (link.crossOrigin === "anonymous") fetchOpts.credentials = "omit";
    else fetchOpts.credentials = "same-origin";
    return fetchOpts;
  }
  function processPreload(link) {
    if (link.ep)
      return;
    link.ep = true;
    const fetchOpts = getFetchOpts(link);
    fetch(link.href, fetchOpts);
  }
})();
class AppState {
  constructor() {
    this.currentPage = "tracker";
    this.isFirstLaunch = true;
    this.userData = {
      dailyVapingCount: 0,
      cessationSpeed: "normal",
      startDate: null,
      currentDay: 1,
      breaksRemaining: 0,
      totalBreaks: 0,
      breaksTaken: 0
    };
    this.init();
  }
  init() {
    try {
      console.log("Initializing ByeVape AppState...");
      this.loadUserData();
      this.checkFirstLaunch();
      this.initializeNavigation();
      this.startApp();
      console.log("ByeVape AppState initialized successfully");
    } catch (error) {
      console.error("Error during AppState initialization:", error);
      this.showAlert("App initialization failed. Please refresh the page.", "error");
    }
  }
  loadUserData() {
    try {
      const savedData = localStorage.getItem("byevape-userdata");
      if (savedData) {
        this.userData = { ...this.userData, ...JSON.parse(savedData) };
        this.isFirstLaunch = false;
        console.log("User data loaded successfully");
      } else {
        console.log("No saved user data found - first launch");
      }
    } catch (error) {
      console.error("Error loading user data:", error);
      this.userData = {
        dailyVapingCount: 0,
        cessationSpeed: "normal",
        startDate: null,
        currentDay: 1,
        breaksRemaining: 0,
        totalBreaks: 0,
        breaksTaken: 0
      };
    }
  }
  saveUserData() {
    localStorage.setItem("byevape-userdata", JSON.stringify(this.userData));
  }
  checkFirstLaunch() {
    const hasLaunched = localStorage.getItem("byevape-launched");
    if (!hasLaunched) {
      this.isFirstLaunch = true;
      localStorage.setItem("byevape-launched", "true");
    }
  }
  startApp() {
    if (this.isFirstLaunch || !this.userData.startDate) {
      this.showPage("onboarding");
    } else {
      this.checkDailyReset();
      this.showPage("tracker");
      this.updateTrackerData();
      this.startCountdownTimer();
    }
  }
  // ===================================
  // NAVIGATION SYSTEM
  // ===================================
  initializeNavigation() {
    const navItems = document.querySelectorAll(".nav-item");
    navItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        const page = item.getAttribute("data-page");
        this.navigateToPage(page);
      });
    });
  }
  navigateToPage(page) {
    if (this.isFirstLaunch && page !== "onboarding") {
      return;
    }
    this.currentPage = page;
    this.showPage(page);
    this.updateNavigation();
  }
  showPage(pageId) {
    const pages = document.querySelectorAll(".page");
    pages.forEach((page) => {
      page.style.display = "none";
    });
    const targetPage = document.getElementById(`${pageId}-page`);
    if (targetPage) {
      targetPage.style.display = "block";
    }
    const bottomNav = document.getElementById("bottom-nav");
    if (pageId === "onboarding") {
      bottomNav.style.display = "none";
    } else {
      bottomNav.style.display = "flex";
    }
  }
  updateNavigation() {
    const navItems = document.querySelectorAll(".nav-item");
    navItems.forEach((item) => {
      const page = item.getAttribute("data-page");
      if (page === this.currentPage) {
        item.classList.add("active");
      } else {
        item.classList.remove("active");
      }
    });
  }
  // ===================================
  // ONBOARDING FLOW
  // ===================================
  nextOnboardingStep() {
    const dailyCount = document.getElementById("daily-vaping-count").value;
    if (!dailyCount || dailyCount < 1) {
      alert("Please enter a valid number of daily vaping sessions.");
      return;
    }
    this.userData.dailyVapingCount = parseInt(dailyCount);
    document.getElementById("onboarding-step-1").style.display = "none";
    document.getElementById("onboarding-step-2").style.display = "block";
  }
  selectCessationSpeed(speed) {
    const buttons = document.querySelectorAll(".cessation-speed-btn");
    buttons.forEach((btn) => {
      btn.classList.remove("selected");
      if (btn.getAttribute("data-speed") === speed) {
        btn.classList.add("selected");
      }
    });
    this.userData.cessationSpeed = speed;
    document.getElementById("complete-onboarding").disabled = false;
  }
  completeOnboarding() {
    if (!this.userData.cessationSpeed) {
      alert("Please select a cessation speed.");
      return;
    }
    this.userData.startDate = (/* @__PURE__ */ new Date()).toISOString();
    this.userData.currentDay = 1;
    this.userData.totalBreaks = this.userData.dailyVapingCount;
    this.userData.breaksRemaining = this.userData.dailyVapingCount;
    this.userData.breaksTaken = 0;
    this.saveUserData();
    this.isFirstLaunch = false;
    this.showPage("tracker");
    this.updateTrackerData();
    this.startCountdownTimer();
  }
  // ===================================
  // TRACKER PAGE LOGIC
  // ===================================
  updateTrackerData() {
    const startDate = new Date(this.userData.startDate);
    const today = /* @__PURE__ */ new Date();
    const daysDiff = Math.floor((today - startDate) / (1e3 * 60 * 60 * 24)) + 1;
    this.userData.currentDay = daysDiff;
    document.getElementById("current-day").textContent = this.userData.currentDay;
    document.getElementById("breaks-remaining").textContent = this.userData.breaksRemaining;
    document.getElementById("total-breaks").textContent = this.userData.totalBreaks;
    document.getElementById("breaks-taken").textContent = this.userData.breaksTaken;
    const progressPercent = (this.userData.totalBreaks - this.userData.breaksRemaining) / this.userData.totalBreaks * 100;
    document.getElementById("daily-progress").style.width = `${progressPercent}%`;
    this.updateSettingsData();
  }
  takeVapingBreak() {
    if (this.userData.breaksRemaining > 0) {
      this.userData.breaksRemaining--;
      this.userData.breaksTaken++;
      this.saveUserData();
      this.updateTrackerData();
      this.showAlert("Break taken! Keep up the good work.", "success");
    } else {
      this.showAlert("No breaks remaining for today. Great job!", "info");
    }
  }
  takeEarlyBreak() {
    if (this.userData.breaksRemaining > 0) {
      this.userData.breaksRemaining--;
      this.userData.breaksTaken++;
      this.saveUserData();
      this.updateTrackerData();
      this.showAlert("Early break taken. Extra time will be added to your next break interval.", "warning");
    } else {
      this.showAlert("No breaks remaining for today.", "info");
    }
  }
  // ===================================
  // SETTINGS PAGE LOGIC
  // ===================================
  updateSettingsData() {
    document.getElementById("settings-daily-breaks").textContent = this.userData.totalBreaks;
    const speedText = {
      "fast": "Fast (30 days)",
      "normal": "Normal (60 days)",
      "relaxed": "Relaxed (90 days)"
    };
    document.getElementById("settings-cessation-speed").textContent = speedText[this.userData.cessationSpeed];
    document.getElementById("settings-days-completed").textContent = this.userData.currentDay;
  }
  saveSettings() {
    const newDailyBreaks = document.getElementById("modify-daily-breaks").value;
    const newCessationSpeed = document.getElementById("modify-cessation-speed").value;
    if (newDailyBreaks && newDailyBreaks > 0) {
      this.userData.totalBreaks = parseInt(newDailyBreaks);
      this.userData.breaksRemaining = parseInt(newDailyBreaks);
    }
    if (newCessationSpeed) {
      this.userData.cessationSpeed = newCessationSpeed;
    }
    this.saveUserData();
    this.updateTrackerData();
    this.showAlert("Settings saved successfully!", "success");
  }
  resetApp() {
    if (confirm("Are you sure you want to reset the app? This will delete all your progress.")) {
      localStorage.clear();
      location.reload();
    }
  }
  exportData() {
    const dataStr = JSON.stringify(this.userData, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "byevape-data.json";
    link.click();
    URL.revokeObjectURL(url);
    this.showAlert("Data exported successfully!", "success");
  }
  // ===================================
  // TIMER FUNCTIONALITY
  // ===================================
  startCountdownTimer() {
    const hoursPerDay = 18;
    hoursPerDay * 60 / this.userData.totalBreaks;
    const demoMinutes = Math.floor(Math.random() * 4) + 2;
    this.nextBreakTime = new Date(Date.now() + demoMinutes * 60 * 1e3);
    this.updateCountdownDisplay();
    this.countdownInterval = setInterval(() => {
      this.updateCountdownDisplay();
    }, 1e3);
  }
  updateCountdownDisplay() {
    const now = /* @__PURE__ */ new Date();
    const timeLeft = this.nextBreakTime - now;
    if (timeLeft <= 0) {
      document.getElementById("countdown-timer").textContent = "00:00:00";
      this.showAlert("Time for your next vaping break!", "info");
      clearInterval(this.countdownInterval);
      return;
    }
    const hours = Math.floor(timeLeft / (1e3 * 60 * 60));
    const minutes = Math.floor(timeLeft % (1e3 * 60 * 60) / (1e3 * 60));
    const seconds = Math.floor(timeLeft % (1e3 * 60) / 1e3);
    const timeString = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    document.getElementById("countdown-timer").textContent = timeString;
    this.updateCircularProgress(timeLeft);
  }
  updateCircularProgress(timeLeft) {
    const totalTime = this.nextBreakTime - (this.nextBreakTime - 5 * 60 * 1e3);
    const progress = 1 - timeLeft / totalTime;
    const circumference = 2 * Math.PI * 90;
    const offset = circumference * (1 - progress);
    const progressRing = document.querySelector(".progress-ring-fill");
    if (progressRing) {
      progressRing.style.strokeDasharray = circumference;
      progressRing.style.strokeDashoffset = offset;
    }
  }
  // ===================================
  // STATISTICS CALCULATIONS
  // ===================================
  calculateStatistics() {
    const stats = {
      weeklyAverage: 8.2,
      bestDay: { day: "Tuesday", breaks: 6 },
      improvement: -15,
      // percentage
      streak: 3
    };
    return stats;
  }
  updateStatisticsPage() {
    const stats = this.calculateStatistics();
    const weeklyAvg = document.querySelector("#statistics-page .card-body span:nth-child(2)");
    if (weeklyAvg) {
      weeklyAvg.textContent = stats.weeklyAverage;
    }
  }
  // ===================================
  // UTILITY FUNCTIONS
  // ===================================
  showAlert(message, type = "info") {
    try {
      const alert2 = document.createElement("div");
      alert2.className = `alert alert-${type}`;
      alert2.innerHTML = `<strong>${type.charAt(0).toUpperCase() + type.slice(1)}!</strong> ${message}`;
      const currentPage = document.getElementById(`${this.currentPage}-page`);
      if (currentPage) {
        const container = currentPage.querySelector(".container");
        if (container) {
          container.insertBefore(alert2, container.firstChild);
          setTimeout(() => {
            if (alert2.parentNode) {
              alert2.parentNode.removeChild(alert2);
            }
          }, 3e3);
        } else {
          console.warn("Container not found for alert:", message);
        }
      } else {
        console.warn("Current page not found for alert:", message);
        console.log(`${type.toUpperCase()}: ${message}`);
      }
    } catch (error) {
      console.error("Error showing alert:", error);
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }
  formatTime(date) {
    return date.toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  formatDate(date) {
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  }
  // Daily reset functionality
  checkDailyReset() {
    const lastResetDate = localStorage.getItem("byevape-last-reset");
    const today = (/* @__PURE__ */ new Date()).toDateString();
    if (lastResetDate !== today) {
      this.userData.breaksRemaining = this.userData.totalBreaks;
      this.userData.breaksTaken = 0;
      this.applyProgressiveReduction();
      localStorage.setItem("byevape-last-reset", today);
      this.saveUserData();
    }
  }
  applyProgressiveReduction() {
    const daysPassed = this.userData.currentDay;
    const reductionSchedule = {
      "fast": 30,
      // 30 days total
      "normal": 60,
      // 60 days total
      "relaxed": 90
      // 90 days total
    };
    const totalDays = reductionSchedule[this.userData.cessationSpeed];
    const originalBreaks = this.userData.dailyVapingCount;
    const reductionFactor = Math.min(daysPassed / totalDays, 1);
    const newBreakCount = Math.max(1, Math.floor(originalBreaks * (1 - reductionFactor)));
    if (newBreakCount !== this.userData.totalBreaks) {
      this.userData.totalBreaks = newBreakCount;
      this.userData.breaksRemaining = newBreakCount;
      this.showAlert(`Your daily break limit has been reduced to ${newBreakCount}!`, "info");
    }
  }
}
let app;
function initializeApp() {
  try {
    app = new AppState();
    const takeBreakBtn = document.getElementById("take-break-btn");
    const earlyBreakBtn = document.getElementById("early-break-btn");
    if (takeBreakBtn) {
      takeBreakBtn.addEventListener("click", () => app.takeVapingBreak());
    }
    if (earlyBreakBtn) {
      earlyBreakBtn.addEventListener("click", () => app.takeEarlyBreak());
    }
    console.log("ByeVape app initialized successfully");
  } catch (error) {
    console.error("Error initializing ByeVape app:", error);
  }
}
if (window.Capacitor) {
  document.addEventListener("deviceready", initializeApp, false);
} else {
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeApp);
  } else {
    initializeApp();
  }
}
