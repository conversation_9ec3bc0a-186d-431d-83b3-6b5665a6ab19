{"version": 3, "sources": ["../../@capacitor/camera/src/definitions.ts", "../../@capacitor/camera/src/web.ts", "../../@capacitor/camera/src/index.ts"], "sourcesContent": ["import type { PermissionState } from '@capacitor/core';\n\nexport type CameraPermissionState = PermissionState | 'limited';\n\nexport type CameraPermissionType = 'camera' | 'photos';\n\nexport interface PermissionStatus {\n  camera: CameraPermissionState;\n  photos: CameraPermissionState;\n}\n\nexport interface CameraPluginPermissions {\n  permissions: CameraPermissionType[];\n}\n\nexport interface CameraPlugin {\n  /**\n   * Prompt the user to pick a photo from an album, or take a new photo\n   * with the camera.\n   *\n   * @since 1.0.0\n   */\n  getPhoto(options: ImageOptions): Promise<Photo>;\n\n  /**\n   * Allows the user to pick multiple pictures from the photo gallery.\n   * On iOS 13 and older it only allows to pick one picture.\n   *\n   * @since 1.2.0\n   */\n  pickImages(options: GalleryImageOptions): Promise<GalleryPhotos>;\n\n  /**\n   * iOS 14+ Only: Allows the user to update their limited photo library selection.\n   * On iOS 15+ returns all the limited photos after the picker dismissal.\n   * On iOS 14 or if the user gave full access to the photos it returns an empty array.\n   *\n   * @since 4.1.0\n   */\n  pickLimitedLibraryPhotos(): Promise<GalleryPhotos>;\n  /**\n   * iOS 14+ Only: Return an array of photos selected from the limited photo library.\n   *\n   * @since 4.1.0\n   */\n  getLimitedLibraryPhotos(): Promise<GalleryPhotos>;\n\n  /**\n   * Check camera and photo album permissions\n   *\n   * @since 1.0.0\n   */\n  checkPermissions(): Promise<PermissionStatus>;\n\n  /**\n   * Request camera and photo album permissions\n   *\n   * @since 1.0.0\n   */\n  requestPermissions(\n    permissions?: CameraPluginPermissions,\n  ): Promise<PermissionStatus>;\n}\n\nexport interface ImageOptions {\n  /**\n   * The quality of image to return as JPEG, from 0-100\n   * Note: This option is only supported on Android and iOS\n   *\n   * @since 1.0.0\n   */\n  quality?: number;\n  /**\n   * Whether to allow the user to crop or make small edits (platform specific).\n   * On iOS 14+ it's only supported for CameraSource.Camera, but not for CameraSource.Photos.\n   *\n   * @since 1.0.0\n   */\n  allowEditing?: boolean;\n  /**\n   * How the data should be returned. Currently, only 'Base64', 'DataUrl' or 'Uri' is supported\n   *\n   * @since 1.0.0\n   */\n  resultType: CameraResultType;\n  /**\n   * Whether to save the photo to the gallery.\n   * If the photo was picked from the gallery, it will only be saved if edited.\n   * @default: false\n   *\n   * @since 1.0.0\n   */\n  saveToGallery?: boolean;\n  /**\n   * The desired maximum width of the saved image. The aspect ratio is respected.\n   *\n   * @since 1.0.0\n   */\n  width?: number;\n  /**\n   * The desired maximum height of the saved image. The aspect ratio is respected.\n   *\n   * @since 1.0.0\n   */\n  height?: number;\n  /**\n   * Whether to automatically rotate the image \"up\" to correct for orientation\n   * in portrait mode\n   * @default: true\n   *\n   * @since 1.0.0\n   */\n  correctOrientation?: boolean;\n  /**\n   * The source to get the photo from. By default this prompts the user to select\n   * either the photo album or take a photo.\n   * @default: CameraSource.Prompt\n   *\n   * @since 1.0.0\n   */\n  source?: CameraSource;\n  /**\n   * iOS and Web only: The camera direction.\n   * @default: CameraDirection.Rear\n   *\n   * @since 1.0.0\n   */\n  direction?: CameraDirection;\n\n  /**\n   * iOS only: The presentation style of the Camera.\n   * @default: 'fullscreen'\n   *\n   * @since 1.0.0\n   */\n  presentationStyle?: 'fullscreen' | 'popover';\n\n  /**\n   * Web only: Whether to use the PWA Element experience or file input. The\n   * default is to use PWA Elements if installed and fall back to file input.\n   * To always use file input, set this to `true`.\n   *\n   * Learn more about PWA Elements: https://capacitorjs.com/docs/web/pwa-elements\n   *\n   * @since 1.0.0\n   */\n  webUseInput?: boolean;\n\n  /**\n   * Text value to use when displaying the prompt.\n   * @default: 'Photo'\n   *\n   * @since 1.0.0\n   *\n   */\n  promptLabelHeader?: string;\n\n  /**\n   * Text value to use when displaying the prompt.\n   * iOS only: The label of the 'cancel' button.\n   * @default: 'Cancel'\n   *\n   * @since 1.0.0\n   */\n  promptLabelCancel?: string;\n\n  /**\n   * Text value to use when displaying the prompt.\n   * The label of the button to select a saved image.\n   * @default: 'From Photos'\n   *\n   * @since 1.0.0\n   */\n  promptLabelPhoto?: string;\n\n  /**\n   * Text value to use when displaying the prompt.\n   * The label of the button to open the camera.\n   * @default: 'Take Picture'\n   *\n   * @since 1.0.0\n   */\n  promptLabelPicture?: string;\n}\n\nexport interface Photo {\n  /**\n   * The base64 encoded string representation of the image, if using CameraResultType.Base64.\n   *\n   * @since 1.0.0\n   */\n  base64String?: string;\n  /**\n   * The url starting with 'data:image/jpeg;base64,' and the base64 encoded string representation of the image, if using CameraResultType.DataUrl.\n   *\n   * Note: On web, the file format could change depending on the browser.\n   * @since 1.0.0\n   */\n  dataUrl?: string;\n  /**\n   * If using CameraResultType.Uri, the path will contain a full,\n   * platform-specific file URL that can be read later using the Filesystem API.\n   *\n   * @since 1.0.0\n   */\n  path?: string;\n  /**\n   * webPath returns a path that can be used to set the src attribute of an image for efficient\n   * loading and rendering.\n   *\n   * @since 1.0.0\n   */\n  webPath?: string;\n  /**\n   * Exif data, if any, retrieved from the image\n   *\n   * @since 1.0.0\n   */\n  exif?: any;\n  /**\n   * The format of the image, ex: jpeg, png, gif.\n   *\n   * iOS and Android only support jpeg.\n   * Web supports jpeg, png and gif, but the exact availability may vary depending on the browser.\n   * gif is only supported if `webUseInput` is set to `true` or if `source` is set to `Photos`.\n   *\n   * @since 1.0.0\n   */\n  format: string;\n  /**\n   * Whether if the image was saved to the gallery or not.\n   *\n   * On Android and iOS, saving to the gallery can fail if the user didn't\n   * grant the required permissions.\n   * On Web there is no gallery, so always returns false.\n   *\n   * @since 1.1.0\n   */\n  saved: boolean;\n}\n\nexport interface GalleryPhotos {\n  /**\n   * Array of all the picked photos.\n   *\n   * @since 1.2.0\n   */\n  photos: GalleryPhoto[];\n}\n\nexport interface GalleryPhoto {\n  /**\n   * Full, platform-specific file URL that can be read later using the Filesystem API.\n   *\n   * @since 1.2.0\n   */\n  path?: string;\n  /**\n   * webPath returns a path that can be used to set the src attribute of an image for efficient\n   * loading and rendering.\n   *\n   * @since 1.2.0\n   */\n  webPath: string;\n  /**\n   * Exif data, if any, retrieved from the image\n   *\n   * @since 1.2.0\n   */\n  exif?: any;\n  /**\n   * The format of the image, ex: jpeg, png, gif.\n   *\n   * iOS and Android only support jpeg.\n   * Web supports jpeg, png and gif.\n   *\n   * @since 1.2.0\n   */\n  format: string;\n}\nexport interface GalleryImageOptions {\n  /**\n   * The quality of image to return as JPEG, from 0-100\n   * Note: This option is only supported on Android and iOS.\n   *\n   * @since 1.2.0\n   */\n  quality?: number;\n  /**\n   * The desired maximum width of the saved image. The aspect ratio is respected.\n   *\n   * @since 1.2.0\n   */\n  width?: number;\n  /**\n   * The desired maximum height of the saved image. The aspect ratio is respected.\n   *\n   * @since 1.2.0\n   */\n  height?: number;\n  /**\n   * Whether to automatically rotate the image \"up\" to correct for orientation\n   * in portrait mode\n   * @default: true\n   *\n   * @since 1.2.0\n   */\n  correctOrientation?: boolean;\n\n  /**\n   * iOS only: The presentation style of the Camera.\n   * @default: 'fullscreen'\n   *\n   * @since 1.2.0\n   */\n  presentationStyle?: 'fullscreen' | 'popover';\n\n  /**\n   * Maximum number of pictures the user will be able to choose.\n   * Note: This option is only supported on Android 13+ and iOS.\n   *\n   * @default 0 (unlimited)\n   *\n   * @since 1.2.0\n   */\n  limit?: number;\n}\n\nexport enum CameraSource {\n  /**\n   * Prompts the user to select either the photo album or take a photo.\n   */\n  Prompt = 'PROMPT',\n  /**\n   * Take a new photo using the camera.\n   */\n  Camera = 'CAMERA',\n  /**\n   * Pick an existing photo from the gallery or photo album.\n   */\n  Photos = 'PHOTOS',\n}\n\nexport enum CameraDirection {\n  Rear = 'REAR',\n  Front = 'FRONT',\n}\n\nexport enum CameraResultType {\n  Uri = 'uri',\n  Base64 = 'base64',\n  DataUrl = 'dataUrl',\n}\n\n/**\n * @deprecated Use `Photo`.\n * @since 1.0.0\n */\nexport type CameraPhoto = Photo;\n\n/**\n * @deprecated Use `ImageOptions`.\n * @since 1.0.0\n */\nexport type CameraOptions = ImageOptions;\n", "import { WebPlugin, CapacitorException } from '@capacitor/core';\n\nimport { CameraSource, CameraDirection } from './definitions';\nimport type {\n  CameraPlugin,\n  GalleryImageOptions,\n  GalleryPhotos,\n  ImageOptions,\n  PermissionStatus,\n  Photo,\n} from './definitions';\n\nexport class <PERSON><PERSON>eb extends WebPlugin implements CameraPlugin {\n  async getPhoto(options: ImageOptions): Promise<Photo> {\n    // eslint-disable-next-line no-async-promise-executor\n    return new Promise<Photo>(async (resolve, reject) => {\n      if (options.webUseInput || options.source === CameraSource.Photos) {\n        this.fileInputExperience(options, resolve, reject);\n      } else if (options.source === CameraSource.Prompt) {\n        let actionSheet: any = document.querySelector('pwa-action-sheet');\n        if (!actionSheet) {\n          actionSheet = document.createElement('pwa-action-sheet');\n          document.body.appendChild(actionSheet);\n        }\n        actionSheet.header = options.promptLabelHeader || 'Photo';\n        actionSheet.cancelable = false;\n        actionSheet.options = [\n          { title: options.promptLabelPhoto || 'From Photos' },\n          { title: options.promptLabelPicture || 'Take Picture' },\n        ];\n        actionSheet.addEventListener('onSelection', async (e: any) => {\n          const selection = e.detail;\n          if (selection === 0) {\n            this.fileInputExperience(options, resolve, reject);\n          } else {\n            this.cameraExperience(options, resolve, reject);\n          }\n        });\n      } else {\n        this.cameraExperience(options, resolve, reject);\n      }\n    });\n  }\n\n  async pickImages(_options: GalleryImageOptions): Promise<GalleryPhotos> {\n    // eslint-disable-next-line no-async-promise-executor\n    return new Promise<GalleryPhotos>(async (resolve, reject) => {\n      this.multipleFileInputExperience(resolve, reject);\n    });\n  }\n\n  private async cameraExperience(\n    options: ImageOptions,\n    resolve: any,\n    reject: any,\n  ) {\n    if (customElements.get('pwa-camera-modal')) {\n      const cameraModal: any = document.createElement('pwa-camera-modal');\n      cameraModal.facingMode =\n        options.direction === CameraDirection.Front ? 'user' : 'environment';\n      document.body.appendChild(cameraModal);\n      try {\n        await cameraModal.componentOnReady();\n        cameraModal.addEventListener('onPhoto', async (e: any) => {\n          const photo = e.detail;\n\n          if (photo === null) {\n            reject(new CapacitorException('User cancelled photos app'));\n          } else if (photo instanceof Error) {\n            reject(photo);\n          } else {\n            resolve(await this._getCameraPhoto(photo, options));\n          }\n\n          cameraModal.dismiss();\n          document.body.removeChild(cameraModal);\n        });\n\n        cameraModal.present();\n      } catch (e) {\n        this.fileInputExperience(options, resolve, reject);\n      }\n    } else {\n      console.error(\n        `Unable to load PWA Element 'pwa-camera-modal'. See the docs: https://capacitorjs.com/docs/web/pwa-elements.`,\n      );\n      this.fileInputExperience(options, resolve, reject);\n    }\n  }\n\n  private fileInputExperience(\n    options: ImageOptions,\n    resolve: any,\n    reject: any,\n  ) {\n    let input = document.querySelector(\n      '#_capacitor-camera-input',\n    ) as HTMLInputElement;\n\n    const cleanup = () => {\n      input.parentNode?.removeChild(input);\n    };\n\n    if (!input) {\n      input = document.createElement('input') as HTMLInputElement;\n      input.id = '_capacitor-camera-input';\n      input.type = 'file';\n      input.hidden = true;\n      document.body.appendChild(input);\n      input.addEventListener('change', (_e: any) => {\n        const file = input.files![0];\n        let format = 'jpeg';\n\n        if (file.type === 'image/png') {\n          format = 'png';\n        } else if (file.type === 'image/gif') {\n          format = 'gif';\n        }\n\n        if (\n          options.resultType === 'dataUrl' ||\n          options.resultType === 'base64'\n        ) {\n          const reader = new FileReader();\n\n          reader.addEventListener('load', () => {\n            if (options.resultType === 'dataUrl') {\n              resolve({\n                dataUrl: reader.result,\n                format,\n              } as Photo);\n            } else if (options.resultType === 'base64') {\n              const b64 = (reader.result as string).split(',')[1];\n              resolve({\n                base64String: b64,\n                format,\n              } as Photo);\n            }\n\n            cleanup();\n          });\n\n          reader.readAsDataURL(file);\n        } else {\n          resolve({\n            webPath: URL.createObjectURL(file),\n            format: format,\n          });\n          cleanup();\n        }\n      });\n      input.addEventListener('cancel', (_e: any) => {\n        reject(new CapacitorException('User cancelled photos app'));\n        cleanup();\n      });\n    }\n\n    input.accept = 'image/*';\n    (input as any).capture = true;\n\n    if (\n      options.source === CameraSource.Photos ||\n      options.source === CameraSource.Prompt\n    ) {\n      input.removeAttribute('capture');\n    } else if (options.direction === CameraDirection.Front) {\n      (input as any).capture = 'user';\n    } else if (options.direction === CameraDirection.Rear) {\n      (input as any).capture = 'environment';\n    }\n\n    input.click();\n  }\n\n  private multipleFileInputExperience(resolve: any, reject: any) {\n    let input = document.querySelector(\n      '#_capacitor-camera-input-multiple',\n    ) as HTMLInputElement;\n\n    const cleanup = () => {\n      input.parentNode?.removeChild(input);\n    };\n\n    if (!input) {\n      input = document.createElement('input') as HTMLInputElement;\n      input.id = '_capacitor-camera-input-multiple';\n      input.type = 'file';\n      input.hidden = true;\n      input.multiple = true;\n      document.body.appendChild(input);\n      input.addEventListener('change', (_e: any) => {\n        const photos = [];\n        // eslint-disable-next-line @typescript-eslint/prefer-for-of\n        for (let i = 0; i < input.files!.length; i++) {\n          const file = input.files![i];\n          let format = 'jpeg';\n\n          if (file.type === 'image/png') {\n            format = 'png';\n          } else if (file.type === 'image/gif') {\n            format = 'gif';\n          }\n          photos.push({\n            webPath: URL.createObjectURL(file),\n            format: format,\n          });\n        }\n        resolve({ photos });\n        cleanup();\n      });\n      input.addEventListener('cancel', (_e: any) => {\n        reject(new CapacitorException('User cancelled photos app'));\n        cleanup();\n      });\n    }\n\n    input.accept = 'image/*';\n\n    input.click();\n  }\n\n  private _getCameraPhoto(photo: Blob, options: ImageOptions) {\n    return new Promise<Photo>((resolve, reject) => {\n      const reader = new FileReader();\n      const format = photo.type.split('/')[1];\n      if (options.resultType === 'uri') {\n        resolve({\n          webPath: URL.createObjectURL(photo),\n          format: format,\n          saved: false,\n        });\n      } else {\n        reader.readAsDataURL(photo);\n        reader.onloadend = () => {\n          const r = reader.result as string;\n          if (options.resultType === 'dataUrl') {\n            resolve({\n              dataUrl: r,\n              format: format,\n              saved: false,\n            });\n          } else {\n            resolve({\n              base64String: r.split(',')[1],\n              format: format,\n              saved: false,\n            });\n          }\n        };\n        reader.onerror = e => {\n          reject(e);\n        };\n      }\n    });\n  }\n\n  async checkPermissions(): Promise<PermissionStatus> {\n    if (typeof navigator === 'undefined' || !navigator.permissions) {\n      throw this.unavailable('Permissions API not available in this browser');\n    }\n\n    try {\n      // https://developer.mozilla.org/en-US/docs/Web/API/Permissions/query\n      // the specific permissions that are supported varies among browsers that implement the\n      // permissions API, so we need a try/catch in case 'camera' is invalid\n      const permission = await window.navigator.permissions.query({\n        name: 'camera',\n      });\n      return {\n        camera: permission.state,\n        photos: 'granted',\n      };\n    } catch {\n      throw this.unavailable(\n        'Camera permissions are not available in this browser',\n      );\n    }\n  }\n\n  async requestPermissions(): Promise<PermissionStatus> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async pickLimitedLibraryPhotos(): Promise<GalleryPhotos> {\n    throw this.unavailable('Not implemented on web.');\n  }\n\n  async getLimitedLibraryPhotos(): Promise<GalleryPhotos> {\n    throw this.unavailable('Not implemented on web.');\n  }\n}\n\nconst Camera = new CameraWeb();\n\nexport { Camera };\n", "import { registerPlugin } from '@capacitor/core';\n\nimport type { CameraPlugin } from './definitions';\nimport { CameraWeb } from './web';\n\nconst Camera = registerPlugin<CameraPlugin>('Camera', {\n  web: () => new CameraWeb(),\n});\n\nexport * from './definitions';\nexport { Camera };\n"], "mappings": ";;;;;;;AAwUA,IAAY;CAAZ,SAAYA,eAAY;AAItB,EAAAA,cAAA,QAAA,IAAA;AAIA,EAAAA,cAAA,QAAA,IAAA;AAIA,EAAAA,cAAA,QAAA,IAAA;AACF,GAbY,iBAAA,eAAY,CAAA,EAAA;AAexB,IAAY;CAAZ,SAAYC,kBAAe;AACzB,EAAAA,iBAAA,MAAA,IAAA;AACA,EAAAA,iBAAA,OAAA,IAAA;AACF,GAHY,oBAAA,kBAAe,CAAA,EAAA;AAK3B,IAAY;CAAZ,SAAYC,mBAAgB;AAC1B,EAAAA,kBAAA,KAAA,IAAA;AACA,EAAAA,kBAAA,QAAA,IAAA;AACA,EAAAA,kBAAA,SAAA,IAAA;AACF,GAJY,qBAAA,mBAAgB,CAAA,EAAA;;;AChVtB,IAAO,YAAP,cAAyB,UAAS;EACtC,MAAM,SAAS,SAAqB;AAElC,WAAO,IAAI,QAAe,OAAO,SAAS,WAAU;AAClD,UAAI,QAAQ,eAAe,QAAQ,WAAW,aAAa,QAAQ;AACjE,aAAK,oBAAoB,SAAS,SAAS,MAAM;iBACxC,QAAQ,WAAW,aAAa,QAAQ;AACjD,YAAI,cAAmB,SAAS,cAAc,kBAAkB;AAChE,YAAI,CAAC,aAAa;AAChB,wBAAc,SAAS,cAAc,kBAAkB;AACvD,mBAAS,KAAK,YAAY,WAAW;;AAEvC,oBAAY,SAAS,QAAQ,qBAAqB;AAClD,oBAAY,aAAa;AACzB,oBAAY,UAAU;UACpB,EAAE,OAAO,QAAQ,oBAAoB,cAAa;UAClD,EAAE,OAAO,QAAQ,sBAAsB,eAAc;;AAEvD,oBAAY,iBAAiB,eAAe,OAAO,MAAU;AAC3D,gBAAM,YAAY,EAAE;AACpB,cAAI,cAAc,GAAG;AACnB,iBAAK,oBAAoB,SAAS,SAAS,MAAM;iBAC5C;AACL,iBAAK,iBAAiB,SAAS,SAAS,MAAM;;QAElD,CAAC;aACI;AACL,aAAK,iBAAiB,SAAS,SAAS,MAAM;;IAElD,CAAC;EACH;EAEA,MAAM,WAAW,UAA6B;AAE5C,WAAO,IAAI,QAAuB,OAAO,SAAS,WAAU;AAC1D,WAAK,4BAA4B,SAAS,MAAM;IAClD,CAAC;EACH;EAEQ,MAAM,iBACZ,SACA,SACA,QAAW;AAEX,QAAI,eAAe,IAAI,kBAAkB,GAAG;AAC1C,YAAM,cAAmB,SAAS,cAAc,kBAAkB;AAClE,kBAAY,aACV,QAAQ,cAAc,gBAAgB,QAAQ,SAAS;AACzD,eAAS,KAAK,YAAY,WAAW;AACrC,UAAI;AACF,cAAM,YAAY,iBAAgB;AAClC,oBAAY,iBAAiB,WAAW,OAAO,MAAU;AACvD,gBAAM,QAAQ,EAAE;AAEhB,cAAI,UAAU,MAAM;AAClB,mBAAO,IAAI,mBAAmB,2BAA2B,CAAC;qBACjD,iBAAiB,OAAO;AACjC,mBAAO,KAAK;iBACP;AACL,oBAAQ,MAAM,KAAK,gBAAgB,OAAO,OAAO,CAAC;;AAGpD,sBAAY,QAAO;AACnB,mBAAS,KAAK,YAAY,WAAW;QACvC,CAAC;AAED,oBAAY,QAAO;eACZ,GAAG;AACV,aAAK,oBAAoB,SAAS,SAAS,MAAM;;WAE9C;AACL,cAAQ,MACN,6GAA6G;AAE/G,WAAK,oBAAoB,SAAS,SAAS,MAAM;;EAErD;EAEQ,oBACN,SACA,SACA,QAAW;AAEX,QAAI,QAAQ,SAAS,cACnB,0BAA0B;AAG5B,UAAM,UAAU,MAAK;;AACnB,OAAA,KAAA,MAAM,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,YAAY,KAAK;IACrC;AAEA,QAAI,CAAC,OAAO;AACV,cAAQ,SAAS,cAAc,OAAO;AACtC,YAAM,KAAK;AACX,YAAM,OAAO;AACb,YAAM,SAAS;AACf,eAAS,KAAK,YAAY,KAAK;AAC/B,YAAM,iBAAiB,UAAU,CAAC,OAAW;AAC3C,cAAM,OAAO,MAAM,MAAO,CAAC;AAC3B,YAAI,SAAS;AAEb,YAAI,KAAK,SAAS,aAAa;AAC7B,mBAAS;mBACA,KAAK,SAAS,aAAa;AACpC,mBAAS;;AAGX,YACE,QAAQ,eAAe,aACvB,QAAQ,eAAe,UACvB;AACA,gBAAM,SAAS,IAAI,WAAU;AAE7B,iBAAO,iBAAiB,QAAQ,MAAK;AACnC,gBAAI,QAAQ,eAAe,WAAW;AACpC,sBAAQ;gBACN,SAAS,OAAO;gBAChB;eACQ;uBACD,QAAQ,eAAe,UAAU;AAC1C,oBAAM,MAAO,OAAO,OAAkB,MAAM,GAAG,EAAE,CAAC;AAClD,sBAAQ;gBACN,cAAc;gBACd;eACQ;;AAGZ,oBAAO;UACT,CAAC;AAED,iBAAO,cAAc,IAAI;eACpB;AACL,kBAAQ;YACN,SAAS,IAAI,gBAAgB,IAAI;YACjC;WACD;AACD,kBAAO;;MAEX,CAAC;AACD,YAAM,iBAAiB,UAAU,CAAC,OAAW;AAC3C,eAAO,IAAI,mBAAmB,2BAA2B,CAAC;AAC1D,gBAAO;MACT,CAAC;;AAGH,UAAM,SAAS;AACd,UAAc,UAAU;AAEzB,QACE,QAAQ,WAAW,aAAa,UAChC,QAAQ,WAAW,aAAa,QAChC;AACA,YAAM,gBAAgB,SAAS;eACtB,QAAQ,cAAc,gBAAgB,OAAO;AACrD,YAAc,UAAU;eAChB,QAAQ,cAAc,gBAAgB,MAAM;AACpD,YAAc,UAAU;;AAG3B,UAAM,MAAK;EACb;EAEQ,4BAA4B,SAAc,QAAW;AAC3D,QAAI,QAAQ,SAAS,cACnB,mCAAmC;AAGrC,UAAM,UAAU,MAAK;;AACnB,OAAA,KAAA,MAAM,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,YAAY,KAAK;IACrC;AAEA,QAAI,CAAC,OAAO;AACV,cAAQ,SAAS,cAAc,OAAO;AACtC,YAAM,KAAK;AACX,YAAM,OAAO;AACb,YAAM,SAAS;AACf,YAAM,WAAW;AACjB,eAAS,KAAK,YAAY,KAAK;AAC/B,YAAM,iBAAiB,UAAU,CAAC,OAAW;AAC3C,cAAM,SAAS,CAAA;AAEf,iBAAS,IAAI,GAAG,IAAI,MAAM,MAAO,QAAQ,KAAK;AAC5C,gBAAM,OAAO,MAAM,MAAO,CAAC;AAC3B,cAAI,SAAS;AAEb,cAAI,KAAK,SAAS,aAAa;AAC7B,qBAAS;qBACA,KAAK,SAAS,aAAa;AACpC,qBAAS;;AAEX,iBAAO,KAAK;YACV,SAAS,IAAI,gBAAgB,IAAI;YACjC;WACD;;AAEH,gBAAQ,EAAE,OAAM,CAAE;AAClB,gBAAO;MACT,CAAC;AACD,YAAM,iBAAiB,UAAU,CAAC,OAAW;AAC3C,eAAO,IAAI,mBAAmB,2BAA2B,CAAC;AAC1D,gBAAO;MACT,CAAC;;AAGH,UAAM,SAAS;AAEf,UAAM,MAAK;EACb;EAEQ,gBAAgB,OAAa,SAAqB;AACxD,WAAO,IAAI,QAAe,CAAC,SAAS,WAAU;AAC5C,YAAM,SAAS,IAAI,WAAU;AAC7B,YAAM,SAAS,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC;AACtC,UAAI,QAAQ,eAAe,OAAO;AAChC,gBAAQ;UACN,SAAS,IAAI,gBAAgB,KAAK;UAClC;UACA,OAAO;SACR;aACI;AACL,eAAO,cAAc,KAAK;AAC1B,eAAO,YAAY,MAAK;AACtB,gBAAM,IAAI,OAAO;AACjB,cAAI,QAAQ,eAAe,WAAW;AACpC,oBAAQ;cACN,SAAS;cACT;cACA,OAAO;aACR;iBACI;AACL,oBAAQ;cACN,cAAc,EAAE,MAAM,GAAG,EAAE,CAAC;cAC5B;cACA,OAAO;aACR;;QAEL;AACA,eAAO,UAAU,OAAI;AACnB,iBAAO,CAAC;QACV;;IAEJ,CAAC;EACH;EAEA,MAAM,mBAAgB;AACpB,QAAI,OAAO,cAAc,eAAe,CAAC,UAAU,aAAa;AAC9D,YAAM,KAAK,YAAY,+CAA+C;;AAGxE,QAAI;AAIF,YAAM,aAAa,MAAM,OAAO,UAAU,YAAY,MAAM;QAC1D,MAAM;OACP;AACD,aAAO;QACL,QAAQ,WAAW;QACnB,QAAQ;;aAEV,IAAM;AACN,YAAM,KAAK,YACT,sDAAsD;;EAG5D;EAEA,MAAM,qBAAkB;AACtB,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,2BAAwB;AAC5B,UAAM,KAAK,YAAY,yBAAyB;EAClD;EAEA,MAAM,0BAAuB;AAC3B,UAAM,KAAK,YAAY,yBAAyB;EAClD;;AAGF,IAAM,SAAS,IAAI,UAAS;;;AC/R5B,IAAMC,UAAS,eAA6B,UAAU;EACpD,KAAK,MAAM,IAAI,UAAS;CACzB;", "names": ["CameraSource", "CameraDirection", "CameraResultType", "Camera"]}