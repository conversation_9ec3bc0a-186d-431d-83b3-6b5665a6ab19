(function polyfill() {
  const relList = document.createElement("link").relList;
  if (relList && relList.supports && relList.supports("modulepreload")) {
    return;
  }
  for (const link of document.querySelectorAll('link[rel="modulepreload"]')) {
    processPreload(link);
  }
  new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type !== "childList") {
        continue;
      }
      for (const node of mutation.addedNodes) {
        if (node.tagName === "LINK" && node.rel === "modulepreload")
          processPreload(node);
      }
    }
  }).observe(document, { childList: true, subtree: true });
  function getFetchOpts(link) {
    const fetchOpts = {};
    if (link.integrity) fetchOpts.integrity = link.integrity;
    if (link.referrerPolicy) fetchOpts.referrerPolicy = link.referrerPolicy;
    if (link.crossOrigin === "use-credentials")
      fetchOpts.credentials = "include";
    else if (link.crossOrigin === "anonymous") fetchOpts.credentials = "omit";
    else fetchOpts.credentials = "same-origin";
    return fetchOpts;
  }
  function processPreload(link) {
    if (link.ep)
      return;
    link.ep = true;
    const fetchOpts = getFetchOpts(link);
    fetch(link.href, fetchOpts);
  }
})();
const scriptRel = "modulepreload";
const assetsURL = function(dep) {
  return "/" + dep;
};
const seen = {};
const __vitePreload = function preload(baseModule, deps, importerUrl) {
  let promise = Promise.resolve();
  if (deps && deps.length > 0) {
    document.getElementsByTagName("link");
    const cspNonceMeta = document.querySelector(
      "meta[property=csp-nonce]"
    );
    const cspNonce = (cspNonceMeta == null ? void 0 : cspNonceMeta.nonce) || (cspNonceMeta == null ? void 0 : cspNonceMeta.getAttribute("nonce"));
    promise = Promise.allSettled(
      deps.map((dep) => {
        dep = assetsURL(dep);
        if (dep in seen) return;
        seen[dep] = true;
        const isCss = dep.endsWith(".css");
        const cssSelector = isCss ? '[rel="stylesheet"]' : "";
        if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
          return;
        }
        const link = document.createElement("link");
        link.rel = isCss ? "stylesheet" : scriptRel;
        if (!isCss) {
          link.as = "script";
        }
        link.crossOrigin = "";
        link.href = dep;
        if (cspNonce) {
          link.setAttribute("nonce", cspNonce);
        }
        document.head.appendChild(link);
        if (isCss) {
          return new Promise((res, rej) => {
            link.addEventListener("load", res);
            link.addEventListener(
              "error",
              () => rej(new Error(`Unable to preload CSS for ${dep}`))
            );
          });
        }
      })
    );
  }
  function handlePreloadError(err) {
    const e = new Event("vite:preloadError", {
      cancelable: true
    });
    e.payload = err;
    window.dispatchEvent(e);
    if (!e.defaultPrevented) {
      throw err;
    }
  }
  return promise.then((res) => {
    for (const item of res || []) {
      if (item.status !== "rejected") continue;
      handlePreloadError(item.reason);
    }
    return baseModule().catch(handlePreloadError);
  });
};
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var ExceptionCode;
(function(ExceptionCode2) {
  ExceptionCode2["Unimplemented"] = "UNIMPLEMENTED";
  ExceptionCode2["Unavailable"] = "UNAVAILABLE";
})(ExceptionCode || (ExceptionCode = {}));
class CapacitorException extends Error {
  constructor(message, code, data) {
    super(message);
    this.message = message;
    this.code = code;
    this.data = data;
  }
}
const getPlatformId = (win) => {
  var _a, _b;
  if (win === null || win === void 0 ? void 0 : win.androidBridge) {
    return "android";
  } else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {
    return "ios";
  } else {
    return "web";
  }
};
const createCapacitor = (win) => {
  const capCustomPlatform = win.CapacitorCustomPlatform || null;
  const cap = win.Capacitor || {};
  const Plugins = cap.Plugins = cap.Plugins || {};
  const getPlatform = () => {
    return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);
  };
  const isNativePlatform = () => getPlatform() !== "web";
  const isPluginAvailable = (pluginName) => {
    const plugin = registeredPlugins.get(pluginName);
    if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {
      return true;
    }
    if (getPluginHeader(pluginName)) {
      return true;
    }
    return false;
  };
  const getPluginHeader = (pluginName) => {
    var _a;
    return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find((h) => h.name === pluginName);
  };
  const handleError = (err) => win.console.error(err);
  const registeredPlugins = /* @__PURE__ */ new Map();
  const registerPlugin2 = (pluginName, jsImplementations = {}) => {
    const registeredPlugin = registeredPlugins.get(pluginName);
    if (registeredPlugin) {
      console.warn(`Capacitor plugin "${pluginName}" already registered. Cannot register plugins twice.`);
      return registeredPlugin.proxy;
    }
    const platform = getPlatform();
    const pluginHeader = getPluginHeader(pluginName);
    let jsImplementation;
    const loadPluginImplementation = async () => {
      if (!jsImplementation && platform in jsImplementations) {
        jsImplementation = typeof jsImplementations[platform] === "function" ? jsImplementation = await jsImplementations[platform]() : jsImplementation = jsImplementations[platform];
      } else if (capCustomPlatform !== null && !jsImplementation && "web" in jsImplementations) {
        jsImplementation = typeof jsImplementations["web"] === "function" ? jsImplementation = await jsImplementations["web"]() : jsImplementation = jsImplementations["web"];
      }
      return jsImplementation;
    };
    const createPluginMethod = (impl, prop) => {
      var _a, _b;
      if (pluginHeader) {
        const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find((m) => prop === m.name);
        if (methodHeader) {
          if (methodHeader.rtype === "promise") {
            return (options) => cap.nativePromise(pluginName, prop.toString(), options);
          } else {
            return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);
          }
        } else if (impl) {
          return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);
        }
      } else if (impl) {
        return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);
      } else {
        throw new CapacitorException(`"${pluginName}" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);
      }
    };
    const createPluginMethodWrapper = (prop) => {
      let remove;
      const wrapper = (...args) => {
        const p = loadPluginImplementation().then((impl) => {
          const fn = createPluginMethod(impl, prop);
          if (fn) {
            const p2 = fn(...args);
            remove = p2 === null || p2 === void 0 ? void 0 : p2.remove;
            return p2;
          } else {
            throw new CapacitorException(`"${pluginName}.${prop}()" is not implemented on ${platform}`, ExceptionCode.Unimplemented);
          }
        });
        if (prop === "addListener") {
          p.remove = async () => remove();
        }
        return p;
      };
      wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;
      Object.defineProperty(wrapper, "name", {
        value: prop,
        writable: false,
        configurable: false
      });
      return wrapper;
    };
    const addListener = createPluginMethodWrapper("addListener");
    const removeListener = createPluginMethodWrapper("removeListener");
    const addListenerNative = (eventName, callback) => {
      const call = addListener({ eventName }, callback);
      const remove = async () => {
        const callbackId = await call;
        removeListener({
          eventName,
          callbackId
        }, callback);
      };
      const p = new Promise((resolve) => call.then(() => resolve({ remove })));
      p.remove = async () => {
        console.warn(`Using addListener() without 'await' is deprecated.`);
        await remove();
      };
      return p;
    };
    const proxy = new Proxy({}, {
      get(_, prop) {
        switch (prop) {
          case "$$typeof":
            return void 0;
          case "toJSON":
            return () => ({});
          case "addListener":
            return pluginHeader ? addListenerNative : addListener;
          case "removeListener":
            return removeListener;
          default:
            return createPluginMethodWrapper(prop);
        }
      }
    });
    Plugins[pluginName] = proxy;
    registeredPlugins.set(pluginName, {
      name: pluginName,
      proxy,
      platforms: /* @__PURE__ */ new Set([...Object.keys(jsImplementations), ...pluginHeader ? [platform] : []])
    });
    return proxy;
  };
  if (!cap.convertFileSrc) {
    cap.convertFileSrc = (filePath) => filePath;
  }
  cap.getPlatform = getPlatform;
  cap.handleError = handleError;
  cap.isNativePlatform = isNativePlatform;
  cap.isPluginAvailable = isPluginAvailable;
  cap.registerPlugin = registerPlugin2;
  cap.Exception = CapacitorException;
  cap.DEBUG = !!cap.DEBUG;
  cap.isLoggingEnabled = !!cap.isLoggingEnabled;
  return cap;
};
const initCapacitorGlobal = (win) => win.Capacitor = createCapacitor(win);
const Capacitor = /* @__PURE__ */ initCapacitorGlobal(typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
const registerPlugin = Capacitor.registerPlugin;
class WebPlugin {
  constructor() {
    this.listeners = {};
    this.retainedEventArguments = {};
    this.windowListeners = {};
  }
  addListener(eventName, listenerFunc) {
    let firstListener = false;
    const listeners = this.listeners[eventName];
    if (!listeners) {
      this.listeners[eventName] = [];
      firstListener = true;
    }
    this.listeners[eventName].push(listenerFunc);
    const windowListener = this.windowListeners[eventName];
    if (windowListener && !windowListener.registered) {
      this.addWindowListener(windowListener);
    }
    if (firstListener) {
      this.sendRetainedArgumentsForEvent(eventName);
    }
    const remove = async () => this.removeListener(eventName, listenerFunc);
    const p = Promise.resolve({ remove });
    return p;
  }
  async removeAllListeners() {
    this.listeners = {};
    for (const listener in this.windowListeners) {
      this.removeWindowListener(this.windowListeners[listener]);
    }
    this.windowListeners = {};
  }
  notifyListeners(eventName, data, retainUntilConsumed) {
    const listeners = this.listeners[eventName];
    if (!listeners) {
      if (retainUntilConsumed) {
        let args = this.retainedEventArguments[eventName];
        if (!args) {
          args = [];
        }
        args.push(data);
        this.retainedEventArguments[eventName] = args;
      }
      return;
    }
    listeners.forEach((listener) => listener(data));
  }
  hasListeners(eventName) {
    var _a;
    return !!((_a = this.listeners[eventName]) === null || _a === void 0 ? void 0 : _a.length);
  }
  registerWindowListener(windowEventName, pluginEventName) {
    this.windowListeners[pluginEventName] = {
      registered: false,
      windowEventName,
      pluginEventName,
      handler: (event) => {
        this.notifyListeners(pluginEventName, event);
      }
    };
  }
  unimplemented(msg = "not implemented") {
    return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);
  }
  unavailable(msg = "not available") {
    return new Capacitor.Exception(msg, ExceptionCode.Unavailable);
  }
  async removeListener(eventName, listenerFunc) {
    const listeners = this.listeners[eventName];
    if (!listeners) {
      return;
    }
    const index = listeners.indexOf(listenerFunc);
    this.listeners[eventName].splice(index, 1);
    if (!this.listeners[eventName].length) {
      this.removeWindowListener(this.windowListeners[eventName]);
    }
  }
  addWindowListener(handle) {
    window.addEventListener(handle.windowEventName, handle.handler);
    handle.registered = true;
  }
  removeWindowListener(handle) {
    if (!handle) {
      return;
    }
    window.removeEventListener(handle.windowEventName, handle.handler);
    handle.registered = false;
  }
  sendRetainedArgumentsForEvent(eventName) {
    const args = this.retainedEventArguments[eventName];
    if (!args) {
      return;
    }
    delete this.retainedEventArguments[eventName];
    args.forEach((arg) => {
      this.notifyListeners(eventName, arg);
    });
  }
}
const encode = (str) => encodeURIComponent(str).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);
const decode = (str) => str.replace(/(%[\dA-F]{2})+/gi, decodeURIComponent);
class CapacitorCookiesPluginWeb extends WebPlugin {
  async getCookies() {
    const cookies = document.cookie;
    const cookieMap = {};
    cookies.split(";").forEach((cookie) => {
      if (cookie.length <= 0)
        return;
      let [key, value] = cookie.replace(/=/, "CAP_COOKIE").split("CAP_COOKIE");
      key = decode(key).trim();
      value = decode(value).trim();
      cookieMap[key] = value;
    });
    return cookieMap;
  }
  async setCookie(options) {
    try {
      const encodedKey = encode(options.key);
      const encodedValue = encode(options.value);
      const expires = `; expires=${(options.expires || "").replace("expires=", "")}`;
      const path = (options.path || "/").replace("path=", "");
      const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : "";
      document.cookie = `${encodedKey}=${encodedValue || ""}${expires}; path=${path}; ${domain};`;
    } catch (error) {
      return Promise.reject(error);
    }
  }
  async deleteCookie(options) {
    try {
      document.cookie = `${options.key}=; Max-Age=0`;
    } catch (error) {
      return Promise.reject(error);
    }
  }
  async clearCookies() {
    try {
      const cookies = document.cookie.split(";") || [];
      for (const cookie of cookies) {
        document.cookie = cookie.replace(/^ +/, "").replace(/=.*/, `=;expires=${(/* @__PURE__ */ new Date()).toUTCString()};path=/`);
      }
    } catch (error) {
      return Promise.reject(error);
    }
  }
  async clearAllCookies() {
    try {
      await this.clearCookies();
    } catch (error) {
      return Promise.reject(error);
    }
  }
}
registerPlugin("CapacitorCookies", {
  web: () => new CapacitorCookiesPluginWeb()
});
const readBlobAsBase64 = async (blob) => new Promise((resolve, reject) => {
  const reader = new FileReader();
  reader.onload = () => {
    const base64String = reader.result;
    resolve(base64String.indexOf(",") >= 0 ? base64String.split(",")[1] : base64String);
  };
  reader.onerror = (error) => reject(error);
  reader.readAsDataURL(blob);
});
const normalizeHttpHeaders = (headers = {}) => {
  const originalKeys = Object.keys(headers);
  const loweredKeys = Object.keys(headers).map((k) => k.toLocaleLowerCase());
  const normalized = loweredKeys.reduce((acc, key, index) => {
    acc[key] = headers[originalKeys[index]];
    return acc;
  }, {});
  return normalized;
};
const buildUrlParams = (params, shouldEncode = true) => {
  if (!params)
    return null;
  const output = Object.entries(params).reduce((accumulator, entry) => {
    const [key, value] = entry;
    let encodedValue;
    let item;
    if (Array.isArray(value)) {
      item = "";
      value.forEach((str) => {
        encodedValue = shouldEncode ? encodeURIComponent(str) : str;
        item += `${key}=${encodedValue}&`;
      });
      item.slice(0, -1);
    } else {
      encodedValue = shouldEncode ? encodeURIComponent(value) : value;
      item = `${key}=${encodedValue}`;
    }
    return `${accumulator}&${item}`;
  }, "");
  return output.substr(1);
};
const buildRequestInit = (options, extra = {}) => {
  const output = Object.assign({ method: options.method || "GET", headers: options.headers }, extra);
  const headers = normalizeHttpHeaders(options.headers);
  const type = headers["content-type"] || "";
  if (typeof options.data === "string") {
    output.body = options.data;
  } else if (type.includes("application/x-www-form-urlencoded")) {
    const params = new URLSearchParams();
    for (const [key, value] of Object.entries(options.data || {})) {
      params.set(key, value);
    }
    output.body = params.toString();
  } else if (type.includes("multipart/form-data") || options.data instanceof FormData) {
    const form = new FormData();
    if (options.data instanceof FormData) {
      options.data.forEach((value, key) => {
        form.append(key, value);
      });
    } else {
      for (const key of Object.keys(options.data)) {
        form.append(key, options.data[key]);
      }
    }
    output.body = form;
    const headers2 = new Headers(output.headers);
    headers2.delete("content-type");
    output.headers = headers2;
  } else if (type.includes("application/json") || typeof options.data === "object") {
    output.body = JSON.stringify(options.data);
  }
  return output;
};
class CapacitorHttpPluginWeb extends WebPlugin {
  /**
   * Perform an Http request given a set of options
   * @param options Options to build the HTTP request
   */
  async request(options) {
    const requestInit = buildRequestInit(options, options.webFetchExtra);
    const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);
    const url = urlParams ? `${options.url}?${urlParams}` : options.url;
    const response = await fetch(url, requestInit);
    const contentType = response.headers.get("content-type") || "";
    let { responseType = "text" } = response.ok ? options : {};
    if (contentType.includes("application/json")) {
      responseType = "json";
    }
    let data;
    let blob;
    switch (responseType) {
      case "arraybuffer":
      case "blob":
        blob = await response.blob();
        data = await readBlobAsBase64(blob);
        break;
      case "json":
        data = await response.json();
        break;
      case "document":
      case "text":
      default:
        data = await response.text();
    }
    const headers = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });
    return {
      data,
      headers,
      status: response.status,
      url: response.url
    };
  }
  /**
   * Perform an Http GET request given a set of options
   * @param options Options to build the HTTP request
   */
  async get(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "GET" }));
  }
  /**
   * Perform an Http POST request given a set of options
   * @param options Options to build the HTTP request
   */
  async post(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "POST" }));
  }
  /**
   * Perform an Http PUT request given a set of options
   * @param options Options to build the HTTP request
   */
  async put(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "PUT" }));
  }
  /**
   * Perform an Http PATCH request given a set of options
   * @param options Options to build the HTTP request
   */
  async patch(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "PATCH" }));
  }
  /**
   * Perform an Http DELETE request given a set of options
   * @param options Options to build the HTTP request
   */
  async delete(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "DELETE" }));
  }
}
registerPlugin("CapacitorHttp", {
  web: () => new CapacitorHttpPluginWeb()
});
const SplashScreen = registerPlugin("SplashScreen", {
  web: () => __vitePreload(() => import("./web-CkcgTqM7.js"), true ? [] : void 0).then((m) => new m.SplashScreenWeb())
});
class AppState {
  constructor() {
    this.currentPage = "tracker";
    this.isFirstLaunch = true;
    this.userData = {
      dailyVapingCount: 0,
      cessationSpeed: "normal",
      startDate: null,
      currentDay: 1,
      breaksRemaining: 0,
      totalBreaks: 0,
      breaksTaken: 0
    };
    this.init();
  }
  init() {
    try {
      console.log("Initializing ByeVape AppState...");
      this.loadUserData();
      this.checkFirstLaunch();
      this.initializeNavigation();
      this.startApp();
      console.log("ByeVape AppState initialized successfully");
    } catch (error) {
      console.error("Error during AppState initialization:", error);
      this.showAlert("App initialization failed. Please refresh the page.", "error");
    }
  }
  loadUserData() {
    try {
      const savedData = localStorage.getItem("byevape-userdata");
      if (savedData) {
        this.userData = { ...this.userData, ...JSON.parse(savedData) };
        this.isFirstLaunch = false;
        console.log("User data loaded successfully");
      } else {
        console.log("No saved user data found - first launch");
      }
    } catch (error) {
      console.error("Error loading user data:", error);
      this.userData = {
        dailyVapingCount: 0,
        cessationSpeed: "normal",
        startDate: null,
        currentDay: 1,
        breaksRemaining: 0,
        totalBreaks: 0,
        breaksTaken: 0
      };
    }
  }
  saveUserData() {
    localStorage.setItem("byevape-userdata", JSON.stringify(this.userData));
  }
  checkFirstLaunch() {
    const hasLaunched = localStorage.getItem("byevape-launched");
    if (!hasLaunched) {
      this.isFirstLaunch = true;
      localStorage.setItem("byevape-launched", "true");
    }
  }
  startApp() {
    console.log(`🚀 Starting app - isFirstLaunch: ${this.isFirstLaunch}, startDate: ${this.userData.startDate}`);
    if (this.isFirstLaunch || !this.userData.startDate) {
      console.log(`👋 First launch detected - showing onboarding`);
      this.showPage("onboarding");
    } else {
      console.log(`🔄 Returning user - showing tracker`);
      this.checkDailyReset();
      this.showPage("tracker");
      this.updateTrackerData();
      this.startCountdownTimer();
    }
  }
  // ===================================
  // NAVIGATION SYSTEM
  // ===================================
  initializeNavigation() {
    const navItems = document.querySelectorAll(".nav-item");
    navItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        const page = item.getAttribute("data-page");
        this.navigateToPage(page);
      });
    });
  }
  navigateToPage(page) {
    if (this.isFirstLaunch && page !== "onboarding") {
      return;
    }
    this.currentPage = page;
    this.showPage(page);
    this.updateNavigation();
  }
  showPage(pageId) {
    console.log(`🔄 showPage called with pageId: ${pageId}`);
    const pages = document.querySelectorAll(".page");
    console.log(`📄 Found ${pages.length} pages to hide`);
    pages.forEach((page, index) => {
      console.log(`   Hiding page ${index}: ${page.id}`);
      page.style.display = "none";
    });
    const targetPage = document.getElementById(`${pageId}-page`);
    console.log(`🎯 Target page element:`, targetPage);
    if (targetPage) {
      targetPage.style.display = "block";
      console.log(`✅ Showing page: ${pageId}-page`);
      const computedStyle = window.getComputedStyle(targetPage);
      console.log(`📊 Page styles - display: ${computedStyle.display}, visibility: ${computedStyle.visibility}, opacity: ${computedStyle.opacity}`);
      if (pageId === "onboarding") {
        targetPage.classList.add("debug");
        console.log(`🔴 DEBUG: Added debug class to onboarding page`);
        const debugDiv = document.createElement("div");
        debugDiv.id = "debug-indicator";
        debugDiv.style.cssText = `
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: red;
          color: white;
          padding: 20px;
          font-size: 24px;
          z-index: 9999;
          border: 3px solid yellow;
        `;
        debugDiv.textContent = "ONBOARDING PAGE LOADED!";
        document.body.appendChild(debugDiv);
        setTimeout(() => {
          const indicator = document.getElementById("debug-indicator");
          if (indicator) {
            indicator.remove();
          }
        }, 5e3);
      }
    } else {
      console.error(`❌ Target page not found: ${pageId}-page`);
    }
    const bottomNav = document.getElementById("bottom-nav");
    console.log(`🧭 Bottom navigation element:`, bottomNav);
    if (pageId === "onboarding") {
      if (bottomNav) {
        bottomNav.style.display = "none";
        console.log(`🔒 Navigation hidden for onboarding`);
      }
    } else {
      if (bottomNav) {
        bottomNav.style.display = "flex";
        console.log(`🔓 Navigation shown for ${pageId}`);
      }
    }
    console.log(`✨ showPage completed for: ${pageId}`);
  }
  updateNavigation() {
    const navItems = document.querySelectorAll(".nav-item");
    navItems.forEach((item) => {
      const page = item.getAttribute("data-page");
      if (page === this.currentPage) {
        item.classList.add("active");
      } else {
        item.classList.remove("active");
      }
    });
  }
  // ===================================
  // ONBOARDING FLOW
  // ===================================
  nextOnboardingStep() {
    console.log("🔄 Moving to next onboarding step");
    const dailyCountInput = document.getElementById("daily-vaping-count");
    const dailyCount = dailyCountInput.value;
    if (!dailyCount || dailyCount < 1 || dailyCount > 100) {
      this.showAlert("Please enter a valid number between 1 and 100 daily vaping sessions.", "warning");
      dailyCountInput.focus();
      return;
    }
    this.userData.dailyVapingCount = parseInt(dailyCount);
    console.log(`✅ Daily vaping count set to: ${this.userData.dailyVapingCount}`);
    const step1 = document.getElementById("onboarding-step-1");
    const step2 = document.getElementById("onboarding-step-2");
    if (step1 && step2) {
      step1.style.display = "none";
      step2.style.display = "block";
      console.log("✅ Moved to onboarding step 2");
      this.showAlert(`Great! You vape ${dailyCount} times per day. Now let's choose your quit speed.`, "success");
    } else {
      console.error("❌ Onboarding step elements not found");
    }
  }
  selectCessationSpeed(speed) {
    console.log(`🔄 Selecting cessation speed: ${speed}`);
    const buttons = document.querySelectorAll(".cessation-speed-btn");
    buttons.forEach((btn) => {
      btn.classList.remove("selected");
      if (btn.getAttribute("data-speed") === speed) {
        btn.classList.add("selected");
        console.log(`✅ Selected button: ${speed}`);
      }
    });
    this.userData.cessationSpeed = speed;
    const completeBtn = document.getElementById("complete-onboarding");
    if (completeBtn) {
      completeBtn.disabled = false;
      completeBtn.classList.remove("disabled");
      console.log("✅ Complete onboarding button enabled");
    }
    const speedDescriptions = {
      "fast": "You'll quit in 30 days with a more aggressive reduction schedule.",
      "normal": "You'll quit in 60 days with a balanced approach.",
      "relaxed": "You'll quit in 90 days with a gentle, gradual reduction."
    };
    this.showAlert(speedDescriptions[speed], "info");
  }
  async completeOnboarding() {
    console.log("🔄 Completing onboarding...");
    if (!this.userData.cessationSpeed) {
      this.showAlert("Please select a cessation speed before continuing.", "warning");
      return;
    }
    try {
      this.userData.startDate = (/* @__PURE__ */ new Date()).toISOString();
      this.userData.currentDay = 1;
      this.userData.totalBreaks = this.userData.dailyVapingCount;
      this.userData.breaksRemaining = this.userData.dailyVapingCount;
      this.userData.breaksTaken = 0;
      this.saveUserData();
      this.isFirstLaunch = false;
      console.log("✅ Onboarding data saved:", this.userData);
      this.showAlert("Welcome to ByeVape! Your personalized quit plan is ready.", "success");
      setTimeout(() => {
        this.showPage("tracker");
        this.updateTrackerData();
        this.startCountdownTimer();
        console.log("✅ Onboarding completed - navigated to tracker");
      }, 1500);
    } catch (error) {
      console.error("❌ Error completing onboarding:", error);
      this.showAlert("There was an error setting up your account. Please try again.", "error");
    }
  }
  // ===================================
  // TRACKER PAGE LOGIC
  // ===================================
  updateTrackerData() {
    const startDate = new Date(this.userData.startDate);
    const today = /* @__PURE__ */ new Date();
    const daysDiff = Math.floor((today - startDate) / (1e3 * 60 * 60 * 24)) + 1;
    this.userData.currentDay = daysDiff;
    document.getElementById("current-day").textContent = this.userData.currentDay;
    document.getElementById("breaks-remaining").textContent = this.userData.breaksRemaining;
    document.getElementById("total-breaks").textContent = this.userData.totalBreaks;
    document.getElementById("breaks-taken").textContent = this.userData.breaksTaken;
    const progressPercent = (this.userData.totalBreaks - this.userData.breaksRemaining) / this.userData.totalBreaks * 100;
    document.getElementById("daily-progress").style.width = `${progressPercent}%`;
    this.updateSettingsData();
  }
  takeVapingBreak() {
    if (this.userData.breaksRemaining > 0) {
      this.userData.breaksRemaining--;
      this.userData.breaksTaken++;
      this.saveUserData();
      this.updateTrackerData();
      this.showAlert("Break taken! Keep up the good work.", "success");
    } else {
      this.showAlert("No breaks remaining for today. Great job!", "info");
    }
  }
  takeEarlyBreak() {
    if (this.userData.breaksRemaining > 0) {
      this.userData.breaksRemaining--;
      this.userData.breaksTaken++;
      this.saveUserData();
      this.updateTrackerData();
      this.showAlert("Early break taken. Extra time will be added to your next break interval.", "warning");
    } else {
      this.showAlert("No breaks remaining for today.", "info");
    }
  }
  // ===================================
  // SETTINGS PAGE LOGIC
  // ===================================
  updateSettingsData() {
    document.getElementById("settings-daily-breaks").textContent = this.userData.totalBreaks;
    const speedText = {
      "fast": "Fast (30 days)",
      "normal": "Normal (60 days)",
      "relaxed": "Relaxed (90 days)"
    };
    document.getElementById("settings-cessation-speed").textContent = speedText[this.userData.cessationSpeed];
    document.getElementById("settings-days-completed").textContent = this.userData.currentDay;
  }
  saveSettings() {
    const newDailyBreaks = document.getElementById("modify-daily-breaks").value;
    const newCessationSpeed = document.getElementById("modify-cessation-speed").value;
    if (newDailyBreaks && newDailyBreaks > 0) {
      this.userData.totalBreaks = parseInt(newDailyBreaks);
      this.userData.breaksRemaining = parseInt(newDailyBreaks);
    }
    if (newCessationSpeed) {
      this.userData.cessationSpeed = newCessationSpeed;
    }
    this.saveUserData();
    this.updateTrackerData();
    this.showAlert("Settings saved successfully!", "success");
  }
  resetApp() {
    if (confirm("Are you sure you want to reset the app? This will delete all your progress.")) {
      localStorage.clear();
      location.reload();
    }
  }
  exportData() {
    const dataStr = JSON.stringify(this.userData, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "byevape-data.json";
    link.click();
    URL.revokeObjectURL(url);
    this.showAlert("Data exported successfully!", "success");
  }
  // ===================================
  // TIMER FUNCTIONALITY
  // ===================================
  startCountdownTimer() {
    const hoursPerDay = 18;
    hoursPerDay * 60 / this.userData.totalBreaks;
    const demoMinutes = Math.floor(Math.random() * 4) + 2;
    this.nextBreakTime = new Date(Date.now() + demoMinutes * 60 * 1e3);
    this.updateCountdownDisplay();
    this.countdownInterval = setInterval(() => {
      this.updateCountdownDisplay();
    }, 1e3);
  }
  updateCountdownDisplay() {
    const now = /* @__PURE__ */ new Date();
    const timeLeft = this.nextBreakTime - now;
    if (timeLeft <= 0) {
      document.getElementById("countdown-timer").textContent = "00:00:00";
      this.showAlert("Time for your next vaping break!", "info");
      clearInterval(this.countdownInterval);
      return;
    }
    const hours = Math.floor(timeLeft / (1e3 * 60 * 60));
    const minutes = Math.floor(timeLeft % (1e3 * 60 * 60) / (1e3 * 60));
    const seconds = Math.floor(timeLeft % (1e3 * 60) / 1e3);
    const timeString = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    document.getElementById("countdown-timer").textContent = timeString;
    this.updateCircularProgress(timeLeft);
  }
  updateCircularProgress(timeLeft) {
    const totalTime = this.nextBreakTime - (this.nextBreakTime - 5 * 60 * 1e3);
    const progress = 1 - timeLeft / totalTime;
    const circumference = 2 * Math.PI * 90;
    const offset = circumference * (1 - progress);
    const progressRing = document.querySelector(".progress-ring-fill");
    if (progressRing) {
      progressRing.style.strokeDasharray = circumference;
      progressRing.style.strokeDashoffset = offset;
    }
  }
  // ===================================
  // STATISTICS CALCULATIONS
  // ===================================
  calculateStatistics() {
    const stats = {
      weeklyAverage: 8.2,
      bestDay: { day: "Tuesday", breaks: 6 },
      improvement: -15,
      // percentage
      streak: 3
    };
    return stats;
  }
  updateStatisticsPage() {
    const stats = this.calculateStatistics();
    const weeklyAvg = document.querySelector("#statistics-page .card-body span:nth-child(2)");
    if (weeklyAvg) {
      weeklyAvg.textContent = stats.weeklyAverage;
    }
  }
  // ===================================
  // UTILITY FUNCTIONS
  // ===================================
  showAlert(message, type = "info") {
    try {
      const alert = document.createElement("div");
      alert.className = `alert alert-${type}`;
      alert.innerHTML = `<strong>${type.charAt(0).toUpperCase() + type.slice(1)}!</strong> ${message}`;
      const currentPage = document.getElementById(`${this.currentPage}-page`);
      if (currentPage) {
        const container = currentPage.querySelector(".container");
        if (container) {
          container.insertBefore(alert, container.firstChild);
          setTimeout(() => {
            if (alert.parentNode) {
              alert.parentNode.removeChild(alert);
            }
          }, 3e3);
        } else {
          console.warn("Container not found for alert:", message);
        }
      } else {
        console.warn("Current page not found for alert:", message);
        console.log(`${type.toUpperCase()}: ${message}`);
      }
    } catch (error) {
      console.error("Error showing alert:", error);
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }
  formatTime(date) {
    return date.toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit"
    });
  }
  formatDate(date) {
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  }
  // Daily reset functionality
  checkDailyReset() {
    const lastResetDate = localStorage.getItem("byevape-last-reset");
    const today = (/* @__PURE__ */ new Date()).toDateString();
    if (lastResetDate !== today) {
      this.userData.breaksRemaining = this.userData.totalBreaks;
      this.userData.breaksTaken = 0;
      this.applyProgressiveReduction();
      localStorage.setItem("byevape-last-reset", today);
      this.saveUserData();
    }
  }
  applyProgressiveReduction() {
    const daysPassed = this.userData.currentDay;
    const reductionSchedule = {
      "fast": 30,
      // 30 days total
      "normal": 60,
      // 60 days total
      "relaxed": 90
      // 90 days total
    };
    const totalDays = reductionSchedule[this.userData.cessationSpeed];
    const originalBreaks = this.userData.dailyVapingCount;
    const reductionFactor = Math.min(daysPassed / totalDays, 1);
    const newBreakCount = Math.max(1, Math.floor(originalBreaks * (1 - reductionFactor)));
    if (newBreakCount !== this.userData.totalBreaks) {
      this.userData.totalBreaks = newBreakCount;
      this.userData.breaksRemaining = newBreakCount;
      this.showAlert(`Your daily break limit has been reduced to ${newBreakCount}!`, "info");
    }
  }
}
let app;
async function hideSplashScreen() {
  try {
    console.log("🔄 Hiding splash screen...");
    if (window.Capacitor) {
      await SplashScreen.hide();
      console.log("✅ Splash screen hidden successfully");
    } else {
      console.log("ℹ️ Running in browser - no splash screen to hide");
    }
  } catch (error) {
    console.error("❌ Error hiding splash screen:", error);
  }
}
async function initializeApp() {
  try {
    app = new AppState();
    const takeBreakBtn = document.getElementById("take-break-btn");
    const earlyBreakBtn = document.getElementById("early-break-btn");
    if (takeBreakBtn) {
      takeBreakBtn.addEventListener("click", () => app.takeVapingBreak());
    }
    if (earlyBreakBtn) {
      earlyBreakBtn.addEventListener("click", () => app.takeEarlyBreak());
    }
    console.log("ByeVape app initialized successfully");
    await hideSplashScreen();
  } catch (error) {
    console.error("Error initializing ByeVape app:", error);
    await hideSplashScreen();
  }
}
if (window.Capacitor) {
  document.addEventListener("deviceready", initializeApp, false);
} else {
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeApp);
  } else {
    initializeApp();
  }
}
export {
  WebPlugin as W
};
