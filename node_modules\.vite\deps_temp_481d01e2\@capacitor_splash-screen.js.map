{"version": 3, "sources": ["../../@capacitor/splash-screen/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { SplashScreenPlugin } from './definitions';\n\nconst SplashScreen = registerPlugin<SplashScreenPlugin>('SplashScreen', {\n  web: () => import('./web').then(m => new m.SplashScreenWeb()),\n});\n\nexport * from './definitions';\nexport { SplashScreen };\n"], "mappings": ";;;;;AAIA,IAAM,eAAe,eAAmC,gBAAgB;EACtE,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,gBAAe,CAAE;CAC7D;", "names": []}